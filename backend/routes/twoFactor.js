const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const twoFactorAuth = require('../utils/twoFactor');
const { authenticate } = require('../middleware/auth');
const { sendSMS } = require('../utils/sms'); // You'll need to implement this
const rateLimit = require('express-rate-limit');

const router = express.Router();

// Rate limiting for 2FA operations
const twoFactorRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each IP to 10 requests per windowMs
  message: {
    success: false,
    message: 'Too many 2FA attempts, please try again later'
  }
});

// @route   POST /api/2fa/setup/totp
// @desc    Setup TOTP (Time-based One-Time Password)
// @access  Private
router.post('/setup/totp', authenticate, async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Validate setup requirements
    const validation = twoFactorAuth.validateSetupRequirements(user);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        message: 'Setup requirements not met',
        errors: validation.errors
      });
    }

    // Check if TOTP is already enabled
    if (user.twoFactor?.totpEnabled) {
      return res.status(400).json({
        success: false,
        message: 'TOTP is already enabled'
      });
    }

    // Generate new secret
    const secretData = twoFactorAuth.generateSecret(user.email);
    const qrCode = await twoFactorAuth.generateQRCode(secretData.otpauthUrl);

    // Store secret temporarily (not enabled yet)
    user.twoFactor = user.twoFactor || {};
    user.twoFactor.totpSecret = secretData.secret;
    await user.save();

    res.json({
      success: true,
      data: {
        secret: secretData.manualEntryKey,
        qrCode: qrCode,
        backupCodes: null // Will be generated after verification
      }
    });

  } catch (error) {
    console.error('TOTP setup error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during TOTP setup'
    });
  }
});

// @route   POST /api/2fa/verify/totp
// @desc    Verify and enable TOTP
// @access  Private
router.post('/verify/totp', 
  authenticate,
  twoFactorRateLimit,
  [
    body('token').isLength({ min: 6, max: 6 }).isNumeric()
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Invalid token format',
          errors: errors.array()
        });
      }

      const { token } = req.body;
      const user = await User.findById(req.user.id);

      if (!user || !user.twoFactor?.totpSecret) {
        return res.status(400).json({
          success: false,
          message: 'TOTP setup not initiated'
        });
      }

      // Verify token
      const isValid = twoFactorAuth.verifyToken(token, user.twoFactor.totpSecret);
      
      // Log attempt
      const attempt = twoFactorAuth.logAttempt(
        user._id, 
        isValid, 
        'totp', 
        req.ip
      );
      
      user.twoFactor.attempts = user.twoFactor.attempts || [];
      user.twoFactor.attempts.push(attempt);

      if (!isValid) {
        await user.save();
        return res.status(400).json({
          success: false,
          message: 'Invalid verification code'
        });
      }

      // Enable TOTP and generate backup codes
      user.twoFactor.totpEnabled = true;
      user.twoFactor.lastVerified = new Date();
      
      const backupCodes = twoFactorAuth.generateBackupCodes();
      const hashedBackupCodes = twoFactorAuth.hashBackupCodes(backupCodes);
      user.twoFactor.backupCodes = hashedBackupCodes;

      await user.save();

      res.json({
        success: true,
        message: 'TOTP enabled successfully',
        data: {
          backupCodes: backupCodes
        }
      });

    } catch (error) {
      console.error('TOTP verification error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error during TOTP verification'
      });
    }
  }
);

// @route   POST /api/2fa/disable/totp
// @desc    Disable TOTP
// @access  Private
router.post('/disable/totp',
  authenticate,
  [
    body('password').notEmpty(),
    body('token').optional().isLength({ min: 6, max: 6 }).isNumeric()
  ],
  async (req, res) => {
    try {
      const { password, token } = req.body;
      const user = await User.findById(req.user.id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Verify password
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        return res.status(400).json({
          success: false,
          message: 'Invalid password'
        });
      }

      // If TOTP is enabled, require token verification
      if (user.twoFactor?.totpEnabled && token) {
        const isTokenValid = twoFactorAuth.verifyToken(token, user.twoFactor.totpSecret);
        if (!isTokenValid) {
          return res.status(400).json({
            success: false,
            message: 'Invalid verification code'
          });
        }
      }

      // Disable TOTP
      user.twoFactor.totpEnabled = false;
      user.twoFactor.totpSecret = undefined;
      user.twoFactor.backupCodes = [];
      
      await user.save();

      res.json({
        success: true,
        message: 'TOTP disabled successfully'
      });

    } catch (error) {
      console.error('TOTP disable error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error during TOTP disable'
      });
    }
  }
);

// @route   POST /api/2fa/verify
// @desc    Verify 2FA for sensitive operations
// @access  Private
router.post('/verify',
  authenticate,
  twoFactorRateLimit,
  [
    body('method').isIn(['totp', 'sms', 'backup']),
    body('code').notEmpty(),
    body('operation').optional().isString()
  ],
  async (req, res) => {
    try {
      const { method, code, operation } = req.body;
      const user = await User.findById(req.user.id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Check rate limiting
      const rateCheck = twoFactorAuth.checkRateLimit(
        user._id,
        user.twoFactor?.attempts || []
      );

      if (!rateCheck.allowed) {
        return res.status(429).json({
          success: false,
          message: 'Too many failed attempts',
          resetTime: rateCheck.resetTime
        });
      }

      let isValid = false;

      switch (method) {
        case 'totp':
          if (!user.twoFactor?.totpEnabled) {
            return res.status(400).json({
              success: false,
              message: 'TOTP not enabled'
            });
          }
          isValid = twoFactorAuth.verifyToken(code, user.twoFactor.totpSecret);
          break;

        case 'backup':
          if (!user.twoFactor?.totpEnabled || !user.twoFactor?.backupCodes) {
            return res.status(400).json({
              success: false,
              message: 'Backup codes not available'
            });
          }
          isValid = twoFactorAuth.verifyBackupCode(code, user.twoFactor.backupCodes);
          break;

        case 'sms':
          // SMS verification would be implemented here
          // For now, return not implemented
          return res.status(501).json({
            success: false,
            message: 'SMS verification not implemented yet'
          });

        default:
          return res.status(400).json({
            success: false,
            message: 'Invalid verification method'
          });
      }

      // Log attempt
      const attempt = twoFactorAuth.logAttempt(
        user._id,
        isValid,
        method,
        req.ip
      );

      user.twoFactor.attempts = user.twoFactor.attempts || [];
      user.twoFactor.attempts.push(attempt);

      if (isValid) {
        user.twoFactor.lastVerified = new Date();
      }

      await user.save();

      if (!isValid) {
        return res.status(400).json({
          success: false,
          message: 'Invalid verification code'
        });
      }

      res.json({
        success: true,
        message: 'Verification successful',
        data: {
          verified: true,
          method: method,
          timestamp: new Date()
        }
      });

    } catch (error) {
      console.error('2FA verification error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error during verification'
      });
    }
  }
);

// @route   GET /api/2fa/status
// @desc    Get 2FA status and available methods
// @access  Private
router.get('/status', authenticate, async (req, res) => {
  try {
    const user = await User.findById(req.user.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const availableMethods = twoFactorAuth.getAvailableMethods(user);
    const hasAnyEnabled = twoFactorAuth.hasAnyMethodEnabled(user);
    const recommendedMethod = twoFactorAuth.getRecommendedMethod(user);

    res.json({
      success: true,
      data: {
        enabled: hasAnyEnabled,
        methods: availableMethods,
        recommendedMethod: recommendedMethod,
        lastVerified: user.twoFactor?.lastVerified,
        backupCodesCount: user.twoFactor?.backupCodes?.filter(code => !code.used).length || 0
      }
    });

  } catch (error) {
    console.error('2FA status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting 2FA status'
    });
  }
});

// @route   POST /api/2fa/regenerate-backup-codes
// @desc    Regenerate backup codes
// @access  Private
router.post('/regenerate-backup-codes',
  authenticate,
  [
    body('token').isLength({ min: 6, max: 6 }).isNumeric()
  ],
  async (req, res) => {
    try {
      const { token } = req.body;
      const user = await User.findById(req.user.id);

      if (!user || !user.twoFactor?.totpEnabled) {
        return res.status(400).json({
          success: false,
          message: 'TOTP not enabled'
        });
      }

      // Verify current TOTP token
      const isValid = twoFactorAuth.verifyToken(token, user.twoFactor.totpSecret);
      if (!isValid) {
        return res.status(400).json({
          success: false,
          message: 'Invalid verification code'
        });
      }

      // Generate new backup codes
      const backupCodes = twoFactorAuth.generateBackupCodes();
      const hashedBackupCodes = twoFactorAuth.hashBackupCodes(backupCodes);
      
      user.twoFactor.backupCodes = hashedBackupCodes;
      await user.save();

      res.json({
        success: true,
        message: 'Backup codes regenerated successfully',
        data: {
          backupCodes: backupCodes
        }
      });

    } catch (error) {
      console.error('Backup codes regeneration error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error regenerating backup codes'
      });
    }
  }
);

module.exports = router;
