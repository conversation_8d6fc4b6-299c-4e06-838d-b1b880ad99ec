const twoFactorAuth = require('../utils/twoFactor');
const User = require('../models/User');

/**
 * Two-Factor Authentication Middleware
 * Protects sensitive operations with 2FA verification
 */

/**
 * Require 2FA verification for sensitive operations
 */
const requireTwoFactor = (operation = 'general') => {
  return async (req, res, next) => {
    try {
      // Skip if user is not authenticated
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      const user = await User.findById(req.user.id);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Check if 2FA is required for this operation
      const isRequired = twoFactorAuth.isRequired(operation, user.twoFactor);
      
      // If 2FA is not required, proceed
      if (!isRequired) {
        return next();
      }

      // Check if user has any 2FA method enabled
      const hasAnyEnabled = twoFactorAuth.hasAnyMethodEnabled(user);
      if (!hasAnyEnabled) {
        return res.status(403).json({
          success: false,
          message: 'Two-factor authentication is required for this operation',
          errorCode: '2FA_SETUP_REQUIRED',
          data: {
            operation,
            setupRequired: true,
            availableMethods: twoFactorAuth.getAvailableMethods(user)
          }
        });
      }

      // Check for recent 2FA verification (within last 10 minutes for sensitive operations)
      const recentVerificationWindow = getVerificationWindow(operation);
      const lastVerified = user.twoFactor?.lastVerified;
      
      if (lastVerified) {
        const timeSinceVerification = Date.now() - lastVerified.getTime();
        if (timeSinceVerification < recentVerificationWindow) {
          // Recent verification is valid, proceed
          return next();
        }
      }

      // 2FA verification required
      return res.status(403).json({
        success: false,
        message: 'Two-factor authentication verification required',
        errorCode: '2FA_VERIFICATION_REQUIRED',
        data: {
          operation,
          verificationRequired: true,
          availableMethods: twoFactorAuth.getAvailableMethods(user),
          lastVerified: lastVerified
        }
      });

    } catch (error) {
      console.error('2FA middleware error:', error);
      return res.status(500).json({
        success: false,
        message: 'Server error in 2FA verification'
      });
    }
  };
};

/**
 * Get verification window based on operation sensitivity
 */
function getVerificationWindow(operation) {
  const windows = {
    'payment': 5 * 60 * 1000,        // 5 minutes for payments
    'tender_submission': 10 * 60 * 1000,  // 10 minutes for tender submission
    'bid_placement': 5 * 60 * 1000,   // 5 minutes for bid placement
    'password_change': 0,             // Always require fresh verification
    'financial_operations': 5 * 60 * 1000, // 5 minutes for financial ops
    'admin_actions': 2 * 60 * 1000,   // 2 minutes for admin actions
    'general': 15 * 60 * 1000         // 15 minutes for general operations
  };

  return windows[operation] || windows.general;
}

/**
 * Verify 2FA token from request headers
 */
const verifyTwoFactorToken = async (req, res, next) => {
  try {
    const twoFactorToken = req.header('X-2FA-Token');
    const twoFactorMethod = req.header('X-2FA-Method') || 'totp';

    if (!twoFactorToken) {
      return res.status(400).json({
        success: false,
        message: '2FA token required',
        errorCode: '2FA_TOKEN_MISSING'
      });
    }

    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    let isValid = false;

    switch (twoFactorMethod) {
      case 'totp':
        if (!user.twoFactor?.totpEnabled) {
          return res.status(400).json({
            success: false,
            message: 'TOTP not enabled'
          });
        }
        isValid = twoFactorAuth.verifyToken(twoFactorToken, user.twoFactor.totpSecret);
        break;

      case 'backup':
        if (!user.twoFactor?.totpEnabled || !user.twoFactor?.backupCodes) {
          return res.status(400).json({
            success: false,
            message: 'Backup codes not available'
          });
        }
        isValid = twoFactorAuth.verifyBackupCode(twoFactorToken, user.twoFactor.backupCodes);
        break;

      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid 2FA method'
        });
    }

    // Log attempt
    const attempt = twoFactorAuth.logAttempt(
      user._id,
      isValid,
      twoFactorMethod,
      req.ip
    );

    user.twoFactor.attempts = user.twoFactor.attempts || [];
    user.twoFactor.attempts.push(attempt);

    if (isValid) {
      user.twoFactor.lastVerified = new Date();
    }

    await user.save();

    if (!isValid) {
      return res.status(400).json({
        success: false,
        message: 'Invalid 2FA token'
      });
    }

    // Add verification info to request
    req.twoFactorVerified = {
      method: twoFactorMethod,
      timestamp: new Date()
    };

    next();

  } catch (error) {
    console.error('2FA token verification error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error in 2FA token verification'
    });
  }
};

/**
 * Check if 2FA is enabled for user
 */
const checkTwoFactorEnabled = async (req, res, next) => {
  try {
    if (!req.user) {
      return next();
    }

    const user = await User.findById(req.user.id);
    if (!user) {
      return next();
    }

    req.twoFactorEnabled = twoFactorAuth.hasAnyMethodEnabled(user);
    req.twoFactorMethods = twoFactorAuth.getAvailableMethods(user);

    next();

  } catch (error) {
    console.error('2FA check error:', error);
    next(); // Continue even if check fails
  }
};

/**
 * Conditional 2FA middleware - only require if enabled
 */
const conditionalTwoFactor = (operation = 'general') => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return next();
      }

      const user = await User.findById(req.user.id);
      if (!user) {
        return next();
      }

      // If user has 2FA enabled, require verification
      if (twoFactorAuth.hasAnyMethodEnabled(user)) {
        return requireTwoFactor(operation)(req, res, next);
      }

      // If 2FA not enabled, proceed without verification
      next();

    } catch (error) {
      console.error('Conditional 2FA error:', error);
      return res.status(500).json({
        success: false,
        message: 'Server error in 2FA check'
      });
    }
  };
};

/**
 * Rate limiting for 2FA attempts
 */
const rateLimitTwoFactor = async (req, res, next) => {
  try {
    if (!req.user) {
      return next();
    }

    const user = await User.findById(req.user.id);
    if (!user) {
      return next();
    }

    const rateCheck = twoFactorAuth.checkRateLimit(
      user._id,
      user.twoFactor?.attempts || []
    );

    if (!rateCheck.allowed) {
      return res.status(429).json({
        success: false,
        message: 'Too many 2FA attempts',
        errorCode: '2FA_RATE_LIMITED',
        data: {
          resetTime: rateCheck.resetTime,
          attemptsRemaining: rateCheck.attemptsRemaining
        }
      });
    }

    req.twoFactorRateLimit = rateCheck;
    next();

  } catch (error) {
    console.error('2FA rate limit error:', error);
    next(); // Continue even if rate limit check fails
  }
};

module.exports = {
  requireTwoFactor,
  verifyTwoFactorToken,
  checkTwoFactorEnabled,
  conditionalTwoFactor,
  rateLimitTwoFactor
};
