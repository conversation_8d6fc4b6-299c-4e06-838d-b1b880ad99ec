// Enhanced Error handling middleware
const logger = require('../utils/logger');

/**
 * Enhanced error handler with user-friendly messages
 */
const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Log error with <PERSON> (include user info if available)
  logger.error(err.stack || err.message || err, {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id || 'anonymous',
    timestamp: new Date().toISOString(),
    errorCode: err.code || 'UNKNOWN_ERROR'
  });

  // Enhanced error type handling with user-friendly messages

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = 'المورد المطلوب غير موجود';
    error = {
      statusCode: 404,
      message,
      errorCode: 'RESOURCE_NOT_FOUND',
      userMessage: 'العنصر الذي تبحث عنه غير موجود'
    };
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    const field = Object.keys(err.keyValue)[0];
    const value = err.keyValue[field];
    const message = `${field} '${value}' موجود مسبقاً`;
    error = {
      statusCode: 400,
      message,
      errorCode: 'DUPLICATE_ENTRY',
      userMessage: 'هذه البيانات موجودة مسبقاً في النظام'
    };
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const messages = Object.values(err.errors).map(val => val.message);
    const message = messages.join(', ');
    error = {
      statusCode: 400,
      message,
      errorCode: 'VALIDATION_ERROR',
      userMessage: 'يرجى التحقق من البيانات المدخلة',
      validationErrors: messages
    };
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    const message = 'رمز المصادقة غير صحيح';
    error = {
      statusCode: 401,
      message,
      errorCode: 'INVALID_TOKEN',
      userMessage: 'انتهت صلاحية جلسة العمل، يرجى تسجيل الدخول مرة أخرى'
    };
  }

  if (err.name === 'TokenExpiredError') {
    const message = 'انتهت صلاحية رمز المصادقة';
    error = {
      statusCode: 401,
      message,
      errorCode: 'TOKEN_EXPIRED',
      userMessage: 'انتهت صلاحية جلسة العمل، يرجى تسجيل الدخول مرة أخرى'
    };
  }

  // Database connection errors
  if (err.name === 'MongoNetworkError' || err.name === 'MongooseServerSelectionError') {
    const message = 'خطأ في الاتصال بقاعدة البيانات';
    error = {
      statusCode: 503,
      message,
      errorCode: 'DATABASE_CONNECTION_ERROR',
      userMessage: 'خدمة غير متاحة مؤقتاً، يرجى المحاولة لاحقاً'
    };
  }

  // File upload errors
  if (err.code === 'LIMIT_FILE_SIZE') {
    const message = 'حجم الملف كبير جداً';
    error = {
      statusCode: 400,
      message,
      errorCode: 'FILE_TOO_LARGE',
      userMessage: 'حجم الملف يتجاوز الحد المسموح (10 ميجابايت)'
    };
  }

  // Payment errors
  if (err.code === 'PAYMENT_FAILED') {
    const message = 'فشل في معالجة الدفع';
    error = {
      statusCode: 400,
      message,
      errorCode: 'PAYMENT_FAILED',
      userMessage: 'فشل في معالجة الدفع، يرجى التحقق من بيانات الدفع'
    };
  }

  // Default server error
  if (!error.statusCode) {
    error = {
      statusCode: 500,
      message: 'خطأ داخلي في الخادم',
      errorCode: 'INTERNAL_SERVER_ERROR',
      userMessage: 'حدث خطأ غير متوقع، يرجى المحاولة لاحقاً'
    };
  }

  // Prepare response
  const response = {
    success: false,
    message: error.userMessage || error.message || 'حدث خطأ غير متوقع',
    errorCode: error.errorCode || 'UNKNOWN_ERROR',
    timestamp: new Date().toISOString()
  };

  // Add validation errors if present
  if (error.validationErrors) {
    response.validationErrors = error.validationErrors;
  }

  // Add stack trace in development
  if (process.env.NODE_ENV === 'development') {
    response.stack = err.stack;
    response.originalMessage = err.message;
  }

  res.status(error.statusCode).json(response);
};

/**
 * Async error wrapper to catch async errors
 */
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

module.exports = { errorHandler, asyncHandler };

module.exports = { errorHandler };
