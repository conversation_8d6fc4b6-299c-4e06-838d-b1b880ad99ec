# Server Configuration
PORT=5000
NODE_ENV=development
CLIENT_URL=http://localhost:3000

# Database
MONGODB_URI=mongodb://localhost:27017/auction-tender

# JWT Secret
JWT_SECRET=your_super_secret_jwt_key_here

# Email Configuration (Development - Mailtrap)
EMAIL_HOST=smtp.mailtrap.io
EMAIL_PORT=2525
EMAIL_USER=your_mailtrap_username
EMAIL_PASS=your_mailtrap_password
FROM_EMAIL=<EMAIL>

# Email Configuration (Production - SendGrid)
# SENDGRID_USERNAME=apikey
# SENDGRID_PASSWORD=your_sendgrid_api_key

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# File Upload Configuration
MAX_FILE_SIZE=********  # 10MB in bytes
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/jpg,application/pdf

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# Account Activation
ACCOUNT_REVIEW_TIMEOUT_DAYS=3
AUTO_APPROVE_INDIVIDUAL_ACCOUNTS=false

# Auction Configuration
MIN_AUCTION_DURATION_HOURS=1
MAX_AUCTION_DURATION_DAYS=30
DEFAULT_BID_INCREMENT=10
AUTO_EXTEND_TIME_MINUTES=5

# Payment Configuration (Stripe)
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Admin Configuration
SUPER_ADMIN_EMAIL=<EMAIL>
SUPER_ADMIN_PASSWORD=your_secure_admin_password_here
