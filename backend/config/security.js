const crypto = require('crypto');

/**
 * Security Configuration Module
 * Handles secure credential management and validation
 */

class SecurityConfig {
  constructor() {
    this.validateEnvironment();
  }

  /**
   * Validate that all required environment variables are set
   */
  validateEnvironment() {
    const requiredVars = [
      'JWT_SECRET',
      'MONGODB_URI',
      'SUPER_ADMIN_EMAIL',
      'SUPER_ADMIN_PASSWORD'
    ];

    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      console.error('❌ Missing required environment variables:', missingVars);
      throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
    }

    // Validate JWT secret strength
    if (process.env.JWT_SECRET.length < 32) {
      console.warn('⚠️  JWT_SECRET should be at least 32 characters long for security');
    }

    // Check for default/weak values
    this.checkForWeakCredentials();
  }

  /**
   * Check for weak or default credentials
   */
  checkForWeakCredentials() {
    const weakPatterns = [
      'your_',
      'test_',
      'demo_',
      'password123',
      'admin123',
      'SuperSecurePassword123!'
    ];

    const sensitiveVars = [
      'JWT_SECRET',
      'SUPER_ADMIN_PASSWORD',
      'CLOUDINARY_API_SECRET',
      'MADA_SECRET_KEY',
      'MOYASAR_SECRET_KEY'
    ];

    sensitiveVars.forEach(varName => {
      const value = process.env[varName];
      if (value && weakPatterns.some(pattern => value.includes(pattern))) {
        console.warn(`⚠️  ${varName} appears to contain weak or default credentials`);
        if (process.env.NODE_ENV === 'production') {
          throw new Error(`Weak credentials detected in production for ${varName}`);
        }
      }
    });
  }

  /**
   * Generate a secure random string
   */
  generateSecureSecret(length = 64) {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Get JWT configuration
   */
  getJWTConfig() {
    return {
      secret: process.env.JWT_SECRET,
      refreshSecret: process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET + '_refresh',
      accessTokenExpiry: '24h',
      refreshTokenExpiry: '7d'
    };
  }

  /**
   * Get admin configuration
   */
  getAdminConfig() {
    return {
      email: process.env.SUPER_ADMIN_EMAIL,
      password: process.env.SUPER_ADMIN_PASSWORD
    };
  }

  /**
   * Check if running in development mode
   */
  isDevelopment() {
    return process.env.NODE_ENV === 'development';
  }

  /**
   * Check if running in production mode
   */
  isProduction() {
    return process.env.NODE_ENV === 'production';
  }

  /**
   * Get security headers configuration
   */
  getSecurityHeaders() {
    return {
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'"],
          fontSrc: ["'self'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"],
        },
      },
      crossOriginEmbedderPolicy: false,
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
      }
    };
  }
}

module.exports = new SecurityConfig();
