# Production Environment Configuration
# Generated on: 2025-07-12T11:11:09.878Z
# WARNING: Keep these values secure and never commit to version control

# Server Configuration
PORT=5000
NODE_ENV=production
CLIENT_URL=https://your-domain.com
API_BASE_URL=https://api.your-domain.com

# Database - UPDATE WITH YOUR PRODUCTION MONGODB URI
MONGODB_URI=mongodb+srv://username:<EMAIL>/auction_platform_prod?retryWrites=true&w=majority

# JWT Secrets - GENERATED SECURE VALUES
JWT_SECRET=0fd32f6844315a1e2537353e5224daf472a9c17fdbfbb33a49cae04cb4440f76d6570d8af1443338a028106d82b54ef013d6dfe12a1d697c78c7b33259ed5922
JWT_REFRESH_SECRET=afa097903ba9be8dc17f165eb9f4e3efd8c120a21275b6661bba8d53713684a3f261f897140833e1352af3682ae5075019a7837122d538eff12afc82fbe5f625

# Admin Configuration - UPDATE THESE VALUES
SUPER_ADMIN_EMAIL=<EMAIL>
SUPER_ADMIN_PASSWORD=X@^kc1T^eGcD@2aiBRf^T9Ks9h#M8ziV

# Email Configuration (Production - SendGrid)
SENDGRID_USERNAME=apikey
SENDGRID_PASSWORD=your_sendgrid_api_key_here
FROM_EMAIL=<EMAIL>
EMAIL_HOST=smtp.sendgrid.net
EMAIL_PORT=587

# Cloudinary Configuration - UPDATE WITH YOUR PRODUCTION VALUES
CLOUDINARY_CLOUD_NAME=your_production_cloud_name
CLOUDINARY_API_KEY=your_production_api_key
CLOUDINARY_API_SECRET=670a35de6faa65124a55396a2e563c9b775ebcd7c1cc76852d81408e4e170ef4

# File Upload Configuration
MAX_FILE_SIZE=********
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/jpg,application/pdf

# Rate Limiting (Production Settings)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=50

# Account Configuration
ACCOUNT_REVIEW_TIMEOUT_DAYS=7
AUTO_APPROVE_INDIVIDUAL_ACCOUNTS=false

# Auction Configuration
MIN_AUCTION_DURATION_HOURS=1
MAX_AUCTION_DURATION_DAYS=30
DEFAULT_BID_INCREMENT=10
AUTO_EXTEND_TIME_MINUTES=5

# Payment Configuration (Saudi Gateways) - UPDATE WITH PRODUCTION VALUES
# Mada Configuration
MADA_BASE_URL=https://api.mada.sa
MADA_MERCHANT_ID=your_production_mada_merchant_id
MADA_SECRET_KEY=7f0c8eda4d61d71885c142de24307c4ef4e17f999075dbc20574c5e0271879d5

# Moyasar Configuration
MOYASAR_API_KEY=your_production_moyasar_api_key
MOYASAR_SECRET_KEY=05a44a368d9dad8b1848bc3e282acaf1584ad84f55355c11ec9c1c17df5ecf34

# HyperPay Configuration
HYPERPAY_BASE_URL=https://oppwa.com
HYPERPAY_USER_ID=your_production_hyperpay_user_id
HYPERPAY_PASSWORD=h$$9MTuT5C9SdGPi^%%eUExp
HYPERPAY_ENTITY_ID=your_production_hyperpay_entity_id

# Tap Payments Configuration
TAP_API_KEY=your_production_tap_api_key
TAP_SECRET_KEY=d18dd8ae13ff101fd6e44b674f15ea5d2e6b4784fa78412ff3e7d94c540fad2a

# Platform Configuration
PLATFORM_FEE_PERCENTAGE=2.5

# Security Configuration
SESSION_SECRET=ec55bf1b7f41e1726222de12deb2d514015a9cc60a1fca0dca21b3bc69b110ea5e907c2289ef63050f6dc7b65dbc85228e866728f3414d1cec6d5fab63dd34cf
ENCRYPTION_KEY=8b8153daa30e0f9cab1efed27dfb9ecefbb5956aa1623b0d1de4bbcd7b41336e

# Monitoring and Logging
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true
ENABLE_ERROR_TRACKING=true

# SSL Configuration
FORCE_HTTPS=true
TRUST_PROXY=true

# CORS Configuration
ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com

# Two-Factor Authentication
TOTP_SERVICE_NAME=منصة المزادات والمناقصات
TOTP_ISSUER=AuctionPlatform

# Backup Configuration
BACKUP_ENCRYPTION_KEY=7cf88ee78ed41c3e87ac914791f1d1535e2d6edb136241ed90b7dadf0b4a12f1
BACKUP_SCHEDULE=0 2 * * *

# Cache Configuration
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# SMS Configuration (for 2FA)
SMS_PROVIDER=your_sms_provider
SMS_API_KEY=your_sms_api_key
SMS_API_SECRET=abf8d73c234e700cbdd2e366138cbd27b8c653e96c31fc89f409f7e1865294e6
