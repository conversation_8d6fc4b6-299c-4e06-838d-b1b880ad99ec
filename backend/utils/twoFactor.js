const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const crypto = require('crypto');

/**
 * Two-Factor Authentication Utilities
 * Provides TOTP (Time-based One-Time Password) functionality
 */

class TwoFactorAuth {
  constructor() {
    this.appName = 'منصة المزادات والمناقصات';
    this.issuer = 'AuctionPlatform';
  }

  /**
   * Generate a new secret for TOTP
   */
  generateSecret(userEmail) {
    const secret = speakeasy.generateSecret({
      name: `${this.appName} (${userEmail})`,
      issuer: this.issuer,
      length: 32
    });

    return {
      secret: secret.base32,
      otpauthUrl: secret.otpauth_url,
      manualEntryKey: secret.base32
    };
  }

  /**
   * Generate QR code for TOTP setup
   */
  async generateQRCode(otpauthUrl) {
    try {
      const qrCodeDataURL = await QRCode.toDataURL(otpauthUrl, {
        errorCorrectionLevel: 'M',
        type: 'image/png',
        quality: 0.92,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        width: 256
      });

      return qrCodeDataURL;
    } catch (error) {
      console.error('Error generating QR code:', error);
      throw new Error('Failed to generate QR code');
    }
  }

  /**
   * Verify TOTP token
   */
  verifyToken(token, secret, window = 1) {
    try {
      const verified = speakeasy.totp.verify({
        secret: secret,
        encoding: 'base32',
        token: token,
        window: window, // Allow 1 step before/after for clock drift
        time: Math.floor(Date.now() / 1000)
      });

      return verified;
    } catch (error) {
      console.error('Error verifying TOTP token:', error);
      return false;
    }
  }

  /**
   * Generate backup codes for account recovery
   */
  generateBackupCodes(count = 10) {
    const codes = [];
    for (let i = 0; i < count; i++) {
      // Generate 8-character alphanumeric codes
      const code = crypto.randomBytes(4).toString('hex').toUpperCase();
      codes.push(code);
    }
    return codes;
  }

  /**
   * Hash backup codes for secure storage
   */
  hashBackupCodes(codes) {
    return codes.map(code => {
      const hash = crypto.createHash('sha256').update(code).digest('hex');
      return {
        hash,
        used: false,
        createdAt: new Date()
      };
    });
  }

  /**
   * Verify backup code
   */
  verifyBackupCode(inputCode, hashedCodes) {
    const inputHash = crypto.createHash('sha256').update(inputCode.toUpperCase()).digest('hex');
    
    const codeIndex = hashedCodes.findIndex(
      codeObj => codeObj.hash === inputHash && !codeObj.used
    );

    if (codeIndex !== -1) {
      // Mark code as used
      hashedCodes[codeIndex].used = true;
      hashedCodes[codeIndex].usedAt = new Date();
      return true;
    }

    return false;
  }

  /**
   * Generate SMS verification code
   */
  generateSMSCode() {
    return Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit code
  }

  /**
   * Validate SMS code format
   */
  validateSMSCode(code) {
    return /^\d{6}$/.test(code);
  }

  /**
   * Check if 2FA is required for operation
   */
  isRequired(operation, userSettings = {}) {
    const requirementMap = {
      'payment': true,
      'tender_submission': true,
      'bid_placement': true,
      'profile_update': false,
      'password_change': true,
      'account_settings': false,
      'financial_operations': true,
      'admin_actions': true
    };

    // Check user-specific settings
    if (userSettings.twoFactorRequired) {
      return userSettings.twoFactorRequired.includes(operation);
    }

    return requirementMap[operation] || false;
  }

  /**
   * Generate time-based challenge for additional security
   */
  generateTimeChallenge() {
    const timestamp = Math.floor(Date.now() / 1000);
    const challenge = crypto.randomBytes(16).toString('hex');
    
    return {
      challenge,
      timestamp,
      expiresAt: timestamp + 300 // 5 minutes
    };
  }

  /**
   * Verify time-based challenge
   */
  verifyTimeChallenge(challenge, timestamp, providedChallenge) {
    const now = Math.floor(Date.now() / 1000);
    
    // Check if challenge has expired
    if (now > timestamp + 300) {
      return false;
    }

    return challenge === providedChallenge;
  }

  /**
   * Rate limiting for 2FA attempts
   */
  checkRateLimit(userId, attempts = [], maxAttempts = 5, windowMinutes = 15) {
    const now = Date.now();
    const windowMs = windowMinutes * 60 * 1000;
    
    // Filter attempts within the time window
    const recentAttempts = attempts.filter(
      attempt => now - attempt.timestamp < windowMs
    );

    if (recentAttempts.length >= maxAttempts) {
      const oldestAttempt = Math.min(...recentAttempts.map(a => a.timestamp));
      const resetTime = oldestAttempt + windowMs;
      
      return {
        allowed: false,
        resetTime: new Date(resetTime),
        attemptsRemaining: 0
      };
    }

    return {
      allowed: true,
      attemptsRemaining: maxAttempts - recentAttempts.length
    };
  }

  /**
   * Log 2FA attempt
   */
  logAttempt(userId, success, method = 'totp', ip = null) {
    return {
      userId,
      success,
      method,
      ip,
      timestamp: Date.now(),
      userAgent: null // Can be added from request headers
    };
  }

  /**
   * Validate 2FA setup requirements
   */
  validateSetupRequirements(user) {
    const errors = [];

    if (!user.emailVerified) {
      errors.push('يجب تأكيد البريد الإلكتروني أولاً');
    }

    if (!user.profile.phone) {
      errors.push('يجب إضافة رقم الهاتف أولاً');
    }

    if (user.status !== 'approved') {
      errors.push('يجب تفعيل الحساب أولاً');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get 2FA methods available for user
   */
  getAvailableMethods(user) {
    const methods = [];

    // TOTP is always available if email is verified
    if (user.emailVerified) {
      methods.push({
        type: 'totp',
        name: 'تطبيق المصادقة',
        description: 'استخدم تطبيق Google Authenticator أو Authy',
        enabled: user.twoFactor?.totpEnabled || false
      });
    }

    // SMS if phone is verified
    if (user.profile.phone && user.phoneVerified) {
      methods.push({
        type: 'sms',
        name: 'رسالة نصية',
        description: `إرسال رمز إلى ${user.profile.phone}`,
        enabled: user.twoFactor?.smsEnabled || false
      });
    }

    // Backup codes if TOTP is enabled
    if (user.twoFactor?.totpEnabled) {
      methods.push({
        type: 'backup',
        name: 'رموز الاحتياط',
        description: 'استخدم أحد رموز الاحتياط المحفوظة',
        enabled: true
      });
    }

    return methods;
  }

  /**
   * Check if user has any 2FA method enabled
   */
  hasAnyMethodEnabled(user) {
    return !!(
      user.twoFactor?.totpEnabled ||
      user.twoFactor?.smsEnabled
    );
  }

  /**
   * Get recommended 2FA method for user
   */
  getRecommendedMethod(user) {
    if (user.twoFactor?.totpEnabled) {
      return 'totp';
    }
    
    if (user.twoFactor?.smsEnabled) {
      return 'sms';
    }

    // Default recommendation
    if (user.emailVerified) {
      return 'totp';
    }

    return null;
  }
}

module.exports = new TwoFactorAuth();
