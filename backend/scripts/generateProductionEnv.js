#!/usr/bin/env node

/**
 * Production Environment Generator
 * Generates secure environment variables for production deployment
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

class ProductionEnvGenerator {
  constructor() {
    this.envPath = path.join(__dirname, '../.env.production');
    this.templatePath = path.join(__dirname, '../.env');
  }

  /**
   * Generate a cryptographically secure random string
   */
  generateSecureSecret(length = 64) {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Generate a secure password
   */
  generateSecurePassword(length = 24) {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    
    for (let i = 0; i < length; i++) {
      const randomIndex = crypto.randomInt(0, charset.length);
      password += charset[randomIndex];
    }
    
    return password;
  }

  /**
   * Generate production environment variables
   */
  generateProductionEnv() {
    const productionEnv = `# Production Environment Configuration
# Generated on: ${new Date().toISOString()}
# WARNING: Keep these values secure and never commit to version control

# Server Configuration
PORT=5000
NODE_ENV=production
CLIENT_URL=https://your-domain.com
API_BASE_URL=https://api.your-domain.com

# Database - UPDATE WITH YOUR PRODUCTION MONGODB URI
MONGODB_URI=mongodb+srv://username:<EMAIL>/auction_platform_prod?retryWrites=true&w=majority

# JWT Secrets - GENERATED SECURE VALUES
JWT_SECRET=${this.generateSecureSecret(64)}
JWT_REFRESH_SECRET=${this.generateSecureSecret(64)}

# Admin Configuration - UPDATE THESE VALUES
SUPER_ADMIN_EMAIL=<EMAIL>
SUPER_ADMIN_PASSWORD=${this.generateSecurePassword(32)}

# Email Configuration (Production - SendGrid)
SENDGRID_USERNAME=apikey
SENDGRID_PASSWORD=your_sendgrid_api_key_here
FROM_EMAIL=<EMAIL>
EMAIL_HOST=smtp.sendgrid.net
EMAIL_PORT=587

# Cloudinary Configuration - UPDATE WITH YOUR PRODUCTION VALUES
CLOUDINARY_CLOUD_NAME=your_production_cloud_name
CLOUDINARY_API_KEY=your_production_api_key
CLOUDINARY_API_SECRET=${this.generateSecureSecret(32)}

# File Upload Configuration
MAX_FILE_SIZE=********
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/jpg,application/pdf

# Rate Limiting (Production Settings)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=50

# Account Configuration
ACCOUNT_REVIEW_TIMEOUT_DAYS=7
AUTO_APPROVE_INDIVIDUAL_ACCOUNTS=false

# Auction Configuration
MIN_AUCTION_DURATION_HOURS=1
MAX_AUCTION_DURATION_DAYS=30
DEFAULT_BID_INCREMENT=10
AUTO_EXTEND_TIME_MINUTES=5

# Payment Configuration (Saudi Gateways) - UPDATE WITH PRODUCTION VALUES
# Mada Configuration
MADA_BASE_URL=https://api.mada.sa
MADA_MERCHANT_ID=your_production_mada_merchant_id
MADA_SECRET_KEY=${this.generateSecureSecret(32)}

# Moyasar Configuration
MOYASAR_API_KEY=your_production_moyasar_api_key
MOYASAR_SECRET_KEY=${this.generateSecureSecret(32)}

# HyperPay Configuration
HYPERPAY_BASE_URL=https://oppwa.com
HYPERPAY_USER_ID=your_production_hyperpay_user_id
HYPERPAY_PASSWORD=${this.generateSecurePassword(24)}
HYPERPAY_ENTITY_ID=your_production_hyperpay_entity_id

# Tap Payments Configuration
TAP_API_KEY=your_production_tap_api_key
TAP_SECRET_KEY=${this.generateSecureSecret(32)}

# Platform Configuration
PLATFORM_FEE_PERCENTAGE=2.5

# Security Configuration
SESSION_SECRET=${this.generateSecureSecret(64)}
ENCRYPTION_KEY=${this.generateSecureSecret(32)}

# Monitoring and Logging
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true
ENABLE_ERROR_TRACKING=true

# SSL Configuration
FORCE_HTTPS=true
TRUST_PROXY=true

# CORS Configuration
ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com

# Two-Factor Authentication
TOTP_SERVICE_NAME=منصة المزادات والمناقصات
TOTP_ISSUER=AuctionPlatform

# Backup Configuration
BACKUP_ENCRYPTION_KEY=${this.generateSecureSecret(32)}
BACKUP_SCHEDULE=0 2 * * *

# Cache Configuration
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# SMS Configuration (for 2FA)
SMS_PROVIDER=your_sms_provider
SMS_API_KEY=your_sms_api_key
SMS_API_SECRET=${this.generateSecureSecret(32)}
`;

    return productionEnv;
  }

  /**
   * Generate production environment file
   */
  generate() {
    try {
      const productionEnv = this.generateProductionEnv();
      
      // Write to file
      fs.writeFileSync(this.envPath, productionEnv, 'utf8');
      
      console.log('✅ Production environment file generated successfully!');
      console.log(`📁 Location: ${this.envPath}`);
      console.log('');
      console.log('🔒 IMPORTANT SECURITY NOTES:');
      console.log('1. Update all placeholder values with your actual production credentials');
      console.log('2. Never commit this file to version control');
      console.log('3. Store these credentials securely (use a password manager)');
      console.log('4. Rotate these secrets regularly');
      console.log('5. Use environment variable injection in your deployment platform');
      console.log('');
      console.log('📋 NEXT STEPS:');
      console.log('1. Update MONGODB_URI with your production database connection');
      console.log('2. Update SUPER_ADMIN_EMAIL and verify the generated password');
      console.log('3. Configure your payment gateway credentials');
      console.log('4. Set up your email service (SendGrid) credentials');
      console.log('5. Configure your domain URLs');
      console.log('6. Set up SSL certificates');
      console.log('');
      console.log('🚀 Generated secure credentials:');
      console.log(`   - JWT Secret: ${this.generateSecureSecret(64).substring(0, 20)}...`);
      console.log(`   - Admin Password: ${this.generateSecurePassword(32).substring(0, 10)}...`);
      console.log('   - All payment gateway secrets');
      console.log('   - Encryption keys');
      
    } catch (error) {
      console.error('❌ Error generating production environment:', error.message);
      process.exit(1);
    }
  }

  /**
   * Validate existing environment
   */
  validateEnvironment(envPath = this.templatePath) {
    try {
      const envContent = fs.readFileSync(envPath, 'utf8');
      const lines = envContent.split('\n');
      
      const issues = [];
      const warnings = [];
      
      lines.forEach((line, index) => {
        const lineNumber = index + 1;
        
        // Check for weak patterns
        if (line.includes('dev_') || line.includes('test_')) {
          issues.push(`Line ${lineNumber}: Development credential detected: ${line.split('=')[0]}`);
        }
        
        if (line.includes('CHANGE_THIS') || line.includes('your_')) {
          issues.push(`Line ${lineNumber}: Placeholder value detected: ${line.split('=')[0]}`);
        }
        
        if (line.includes('password123') || line.includes('admin123')) {
          issues.push(`Line ${lineNumber}: Weak password detected: ${line.split('=')[0]}`);
        }
        
        // Check JWT secret length
        if (line.startsWith('JWT_SECRET=')) {
          const secret = line.split('=')[1];
          if (secret && secret.length < 32) {
            warnings.push(`Line ${lineNumber}: JWT secret is too short (${secret.length} chars, recommended: 64+)`);
          }
        }
      });
      
      console.log('🔍 ENVIRONMENT VALIDATION REPORT');
      console.log('================================');
      
      if (issues.length === 0) {
        console.log('✅ No critical security issues found');
      } else {
        console.log(`❌ Found ${issues.length} critical security issues:`);
        issues.forEach(issue => console.log(`   ${issue}`));
      }
      
      if (warnings.length > 0) {
        console.log(`⚠️  Found ${warnings.length} warnings:`);
        warnings.forEach(warning => console.log(`   ${warning}`));
      }
      
      return { issues, warnings };
      
    } catch (error) {
      console.error('❌ Error validating environment:', error.message);
      return { issues: ['Could not read environment file'], warnings: [] };
    }
  }
}

// CLI Interface
if (require.main === module) {
  const generator = new ProductionEnvGenerator();
  
  const command = process.argv[2];
  
  switch (command) {
    case 'generate':
      generator.generate();
      break;
      
    case 'validate':
      const envFile = process.argv[3] || generator.templatePath;
      generator.validateEnvironment(envFile);
      break;
      
    default:
      console.log('🔧 Production Environment Generator');
      console.log('');
      console.log('Usage:');
      console.log('  node generateProductionEnv.js generate   - Generate production .env file');
      console.log('  node generateProductionEnv.js validate   - Validate current .env file');
      console.log('');
      console.log('Examples:');
      console.log('  node generateProductionEnv.js generate');
      console.log('  node generateProductionEnv.js validate .env.production');
      break;
  }
}

module.exports = ProductionEnvGenerator;
