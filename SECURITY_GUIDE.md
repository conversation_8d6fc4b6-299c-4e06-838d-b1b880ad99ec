# 🔒 Security Configuration Guide

## ⚠️ CRITICAL SECURITY NOTICE

This application contains security configurations that **MUST** be updated before production deployment.

## 🚨 Immediate Actions Required

### 1. Environment Variables (.env)

**NEVER use the default values in production!** Update these immediately:

```bash
# Generate a strong JWT secret (64+ characters)
JWT_SECRET=your_secure_jwt_secret_here_64_characters_minimum

# Set a strong admin password
SUPER_ADMIN_PASSWORD=your_very_secure_admin_password_here

# Configure real payment gateway credentials
MADA_MERCHANT_ID=your_real_mada_merchant_id
MADA_SECRET_KEY=your_real_mada_secret_key
MOYASAR_API_KEY=your_real_moyasar_api_key
MOYASAR_SECRET_KEY=your_real_moyasar_secret_key

# Configure real Cloudinary credentials
CLOUDINARY_CLOUD_NAME=your_real_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_real_cloudinary_api_key
CLOUDINARY_API_SECRET=your_real_cloudinary_api_secret
```

### 2. Generate Secure Secrets

Use these commands to generate secure secrets:

```bash
# Generate JWT secret
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"

# Generate admin password
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
```

### 3. Test Account Security

**IMPORTANT**: All test accounts use `password123` - these are for development only!

#### Test Accounts (Development Only):
- <EMAIL> / password123
- <EMAIL> / password123
- <EMAIL> / password123
- <EMAIL> / password123

**Action Required**: 
- Delete or disable test accounts in production
- Create real admin accounts with strong passwords
- Implement proper user registration flow

## 🛡️ Security Features Implemented

### 1. JWT Token Security
- Secure token generation with configurable expiry
- Refresh token mechanism
- Token validation middleware

### 2. Password Security
- bcrypt hashing with salt rounds
- Password strength validation
- Account lockout after failed attempts

### 3. Rate Limiting
- API endpoint rate limiting
- Authentication attempt limiting
- Payment operation limiting

### 4. Input Validation
- Request data validation
- SQL injection prevention
- XSS protection

### 5. Security Headers
- CORS configuration
- Content Security Policy
- HSTS headers

## 🔧 Security Configuration

The security configuration is managed in `backend/config/security.js`:

```javascript
const securityConfig = require('./config/security');

// Validates environment variables
// Checks for weak credentials
// Provides secure defaults
```

## 📋 Production Deployment Checklist

### Before Going Live:

- [ ] Update all environment variables with real values
- [ ] Generate strong JWT secrets (64+ characters)
- [ ] Set secure admin passwords
- [ ] Configure real payment gateway credentials
- [ ] Set up proper SSL/TLS certificates
- [ ] Enable production security headers
- [ ] Remove or disable test accounts
- [ ] Configure proper CORS origins
- [ ] Set up monitoring and logging
- [ ] Enable rate limiting in production
- [ ] Configure backup and recovery
- [ ] Set up security scanning

### Environment-Specific Settings:

```bash
# Production
NODE_ENV=production
JWT_SECRET=your_production_jwt_secret_64_chars_minimum
SUPER_ADMIN_PASSWORD=your_production_admin_password

# Staging
NODE_ENV=staging
JWT_SECRET=your_staging_jwt_secret_64_chars_minimum
SUPER_ADMIN_PASSWORD=your_staging_admin_password

# Development
NODE_ENV=development
JWT_SECRET=dev_jwt_secret_key_change_in_production_2025
SUPER_ADMIN_PASSWORD=CHANGE_THIS_SECURE_PASSWORD_IN_PRODUCTION
```

## 🚨 Security Warnings

### Current Vulnerabilities (Development):
1. **Weak JWT Secret**: Using development placeholder
2. **Default Admin Password**: Using placeholder value
3. **Test Credentials**: Multiple test accounts with weak passwords
4. **Payment Gateways**: Using test/placeholder credentials

### Mitigation:
- All issues are flagged with warnings in development
- Security config validates credentials on startup
- Production deployment will fail with weak credentials

## 📞 Security Contact

For security-related issues or questions:
- Review this guide thoroughly
- Update all credentials before production
- Test security configurations in staging environment
- Monitor security logs regularly

## 🔄 Regular Security Maintenance

### Monthly:
- [ ] Rotate JWT secrets
- [ ] Review user accounts
- [ ] Check security logs
- [ ] Update dependencies

### Quarterly:
- [ ] Security audit
- [ ] Penetration testing
- [ ] Credential rotation
- [ ] Security training

---

**Remember**: Security is not a one-time setup but an ongoing process!
