"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./lib/errorHandler.ts":
/*!*****************************!*\
  !*** ./lib/errorHandler.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getErrorMessage: function() { return /* binding */ getErrorMessage; },\n/* harmony export */   handleAPIError: function() { return /* binding */ handleAPIError; },\n/* harmony export */   handleInfo: function() { return /* binding */ handleInfo; },\n/* harmony export */   handleNetworkError: function() { return /* binding */ handleNetworkError; },\n/* harmony export */   handleSuccess: function() { return /* binding */ handleSuccess; },\n/* harmony export */   handleValidationErrors: function() { return /* binding */ handleValidationErrors; },\n/* harmony export */   handleWarning: function() { return /* binding */ handleWarning; },\n/* harmony export */   withErrorHandling: function() { return /* binding */ withErrorHandling; }\n/* harmony export */ });\n// import { toast } from '@/components/ui/use-toast'\n/**\n * Enhanced Error Handler for API and Application Errors\n */ /**\n * Error message translations from English to Arabic\n */ const errorTranslations = {\n    // Authentication errors\n    \"Invalid credentials\": \"البريد الإلكتروني أو كلمة المرور غير صحيحة\",\n    \"User not found\": \"المستخدم غير موجود\",\n    \"Account suspended\": \"تم تعليق الحساب\",\n    \"Account blocked\": \"تم حظر الحساب\",\n    \"Email not verified\": \"يرجى تأكيد البريد الإلكتروني\",\n    \"Token expired\": \"انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى\",\n    \"Invalid token\": \"رمز المصادقة غير صحيح\",\n    // Validation errors\n    \"Validation failed\": \"فشل في التحقق من البيانات\",\n    \"Required field missing\": \"حقل مطلوب مفقود\",\n    \"Invalid email format\": \"تنسيق البريد الإلكتروني غير صحيح\",\n    \"Password too weak\": \"كلمة المرور ضعيفة جداً\",\n    // Resource errors\n    \"Resource not found\": \"المورد المطلوب غير موجود\",\n    \"Access denied\": \"غير مسموح بالوصول\",\n    \"Insufficient permissions\": \"صلاحيات غير كافية\",\n    // Server errors\n    \"Server error\": \"خطأ في الخادم\",\n    \"Service unavailable\": \"الخدمة غير متاحة مؤقتاً\",\n    \"Database connection error\": \"خطأ في الاتصال بقاعدة البيانات\",\n    // Payment errors\n    \"Payment failed\": \"فشل في معالجة الدفع\",\n    \"Insufficient funds\": \"رصيد غير كافي\",\n    \"Invalid payment method\": \"طريقة دفع غير صحيحة\",\n    // File upload errors\n    \"File too large\": \"حجم الملف كبير جداً\",\n    \"Invalid file type\": \"نوع الملف غير مدعوم\",\n    \"Upload failed\": \"فشل في رفع الملف\",\n    // Network errors\n    \"Network error\": \"خطأ في الشبكة\",\n    \"Connection timeout\": \"انتهت مهلة الاتصال\",\n    \"No internet connection\": \"لا يوجد اتصال بالإنترنت\"\n};\n/**\n * Get user-friendly error message in Arabic\n */ const getErrorMessage = (error)=>{\n    var _error_response_data, _error_response, _error_data;\n    // If it's already an Arabic message, return as is\n    if (typeof error === \"string\" && /[\\u0600-\\u06FF]/.test(error)) {\n        return error;\n    }\n    // Extract message from different error formats\n    let message = \"\";\n    if (typeof error === \"string\") {\n        message = error;\n    } else if (error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n        message = error.response.data.message;\n    } else if (error === null || error === void 0 ? void 0 : error.message) {\n        message = error.message;\n    } else if (error === null || error === void 0 ? void 0 : (_error_data = error.data) === null || _error_data === void 0 ? void 0 : _error_data.message) {\n        message = error.data.message;\n    } else {\n        message = \"حدث خطأ غير متوقع\";\n    }\n    // Try to find translation\n    const translation = errorTranslations[message] || Object.keys(errorTranslations).find((key)=>message.toLowerCase().includes(key.toLowerCase()));\n    return translation ? errorTranslations[translation] : message;\n};\n/**\n * Handle API errors with user-friendly messages and logging\n */ const handleAPIError = function(error, context) {\n    let showToast = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n    var _error_response_data, _error_response, _error_response_data1, _error_response1;\n    const errorMessage = getErrorMessage(error);\n    const errorCode = (error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.errorCode) || (error === null || error === void 0 ? void 0 : error.code) || \"UNKNOWN_ERROR\";\n    // Log error for debugging\n    console.error(\"API Error:\", {\n        message: errorMessage,\n        errorCode,\n        context,\n        originalError: error,\n        timestamp: new Date().toISOString()\n    });\n    // Show toast notification if requested\n    if (showToast) {\n        console.error(\"Error:\", errorMessage);\n    // toast({\n    //   title: 'خطأ',\n    //   description: errorMessage,\n    //   variant: 'destructive',\n    //   duration: 5000\n    // })\n    }\n    // Return standardized error object\n    return {\n        success: false,\n        message: errorMessage,\n        errorCode,\n        validationErrors: error === null || error === void 0 ? void 0 : (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data1 = _error_response1.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.validationErrors,\n        timestamp: new Date().toISOString()\n    };\n};\n/**\n * Handle form validation errors\n */ const handleValidationErrors = (errors)=>{\n    const errorMessages = Object.entries(errors).map((param)=>{\n        let [field, messages] = param;\n        return \"\".concat(field, \": \").concat(messages.join(\", \"));\n    }).join(\"\\n\");\n    console.error(\"Validation errors:\", errorMessages);\n// toast({\n//   title: 'خطأ في البيانات',\n//   description: errorMessages,\n//   variant: 'destructive',\n//   duration: 7000\n// })\n};\n/**\n * Handle network errors\n */ const handleNetworkError = (error)=>{\n    var _error_response;\n    let message = \"خطأ في الشبكة\";\n    if (!navigator.onLine) {\n        message = \"لا يوجد اتصال بالإنترنت\";\n    } else if (error.code === \"ECONNABORTED\") {\n        message = \"انتهت مهلة الاتصال\";\n    } else if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) >= 500) {\n        message = \"خطأ في الخادم، يرجى المحاولة لاحقاً\";\n    }\n    console.error(\"Network error:\", message);\n// toast({\n//   title: 'مشكلة في الاتصال',\n//   description: message,\n//   variant: 'destructive',\n//   duration: 5000\n// })\n};\n/**\n * Success message handler\n */ const handleSuccess = function(message) {\n    let title = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"نجح العملية\";\n    console.log(\"Success:\", title, message);\n// toast({\n//   title,\n//   description: message,\n//   variant: 'default',\n//   duration: 3000\n// })\n};\n/**\n * Warning message handler\n */ const handleWarning = function(message) {\n    let title = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"تحذير\";\n    toast({\n        title,\n        description: message,\n        variant: \"destructive\",\n        duration: 4000\n    });\n};\n/**\n * Info message handler\n */ const handleInfo = function(message) {\n    let title = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"معلومة\";\n    toast({\n        title,\n        description: message,\n        variant: \"default\",\n        duration: 3000\n    });\n};\n/**\n * Async operation wrapper with error handling\n */ const withErrorHandling = async function(operation, context) {\n    let showToast = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n    try {\n        return await operation();\n    } catch (error) {\n        handleAPIError(error, context, showToast);\n        return null;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/errorHandler.ts\n"));

/***/ })

});