"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./lib/errorHandler.ts":
/*!*****************************!*\
  !*** ./lib/errorHandler.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getErrorMessage: function() { return /* binding */ getErrorMessage; },\n/* harmony export */   handleAPIError: function() { return /* binding */ handleAPIError; },\n/* harmony export */   handleInfo: function() { return /* binding */ handleInfo; },\n/* harmony export */   handleNetworkError: function() { return /* binding */ handleNetworkError; },\n/* harmony export */   handleSuccess: function() { return /* binding */ handleSuccess; },\n/* harmony export */   handleValidationErrors: function() { return /* binding */ handleValidationErrors; },\n/* harmony export */   handleWarning: function() { return /* binding */ handleWarning; },\n/* harmony export */   withErrorHandling: function() { return /* binding */ withErrorHandling; }\n/* harmony export */ });\n// import { toast } from '@/components/ui/use-toast'\n/**\n * Enhanced Error Handler for API and Application Errors\n */ /**\n * Error message translations from English to Arabic\n */ const errorTranslations = {\n    // Authentication errors\n    \"Invalid credentials\": \"البريد الإلكتروني أو كلمة المرور غير صحيحة\",\n    \"User not found\": \"المستخدم غير موجود\",\n    \"Account suspended\": \"تم تعليق الحساب\",\n    \"Account blocked\": \"تم حظر الحساب\",\n    \"Email not verified\": \"يرجى تأكيد البريد الإلكتروني\",\n    \"Token expired\": \"انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى\",\n    \"Invalid token\": \"رمز المصادقة غير صحيح\",\n    // Validation errors\n    \"Validation failed\": \"فشل في التحقق من البيانات\",\n    \"Required field missing\": \"حقل مطلوب مفقود\",\n    \"Invalid email format\": \"تنسيق البريد الإلكتروني غير صحيح\",\n    \"Password too weak\": \"كلمة المرور ضعيفة جداً\",\n    // Resource errors\n    \"Resource not found\": \"المورد المطلوب غير موجود\",\n    \"Access denied\": \"غير مسموح بالوصول\",\n    \"Insufficient permissions\": \"صلاحيات غير كافية\",\n    // Server errors\n    \"Server error\": \"خطأ في الخادم\",\n    \"Service unavailable\": \"الخدمة غير متاحة مؤقتاً\",\n    \"Database connection error\": \"خطأ في الاتصال بقاعدة البيانات\",\n    // Payment errors\n    \"Payment failed\": \"فشل في معالجة الدفع\",\n    \"Insufficient funds\": \"رصيد غير كافي\",\n    \"Invalid payment method\": \"طريقة دفع غير صحيحة\",\n    // File upload errors\n    \"File too large\": \"حجم الملف كبير جداً\",\n    \"Invalid file type\": \"نوع الملف غير مدعوم\",\n    \"Upload failed\": \"فشل في رفع الملف\",\n    // Network errors\n    \"Network error\": \"خطأ في الشبكة\",\n    \"Connection timeout\": \"انتهت مهلة الاتصال\",\n    \"No internet connection\": \"لا يوجد اتصال بالإنترنت\"\n};\n/**\n * Get user-friendly error message in Arabic\n */ const getErrorMessage = (error)=>{\n    var _error_response_data, _error_response, _error_data;\n    // If it's already an Arabic message, return as is\n    if (typeof error === \"string\" && /[\\u0600-\\u06FF]/.test(error)) {\n        return error;\n    }\n    // Extract message from different error formats\n    let message = \"\";\n    if (typeof error === \"string\") {\n        message = error;\n    } else if (error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n        message = error.response.data.message;\n    } else if (error === null || error === void 0 ? void 0 : error.message) {\n        message = error.message;\n    } else if (error === null || error === void 0 ? void 0 : (_error_data = error.data) === null || _error_data === void 0 ? void 0 : _error_data.message) {\n        message = error.data.message;\n    } else {\n        message = \"حدث خطأ غير متوقع\";\n    }\n    // Try to find translation\n    const translation = errorTranslations[message] || Object.keys(errorTranslations).find((key)=>message.toLowerCase().includes(key.toLowerCase()));\n    return translation ? errorTranslations[translation] : message;\n};\n/**\n * Handle API errors with user-friendly messages and logging\n */ const handleAPIError = function(error, context) {\n    let showToast = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n    var _error_response_data, _error_response, _error_response_data1, _error_response1;\n    const errorMessage = getErrorMessage(error);\n    const errorCode = (error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.errorCode) || (error === null || error === void 0 ? void 0 : error.code) || \"UNKNOWN_ERROR\";\n    // Log error for debugging\n    console.error(\"API Error:\", {\n        message: errorMessage,\n        errorCode,\n        context,\n        originalError: error,\n        timestamp: new Date().toISOString()\n    });\n    // Show toast notification if requested\n    if (showToast) {\n        console.error(\"Error:\", errorMessage);\n    // toast({\n    //   title: 'خطأ',\n    //   description: errorMessage,\n    //   variant: 'destructive',\n    //   duration: 5000\n    // })\n    }\n    // Return standardized error object\n    return {\n        success: false,\n        message: errorMessage,\n        errorCode,\n        validationErrors: error === null || error === void 0 ? void 0 : (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data1 = _error_response1.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.validationErrors,\n        timestamp: new Date().toISOString()\n    };\n};\n/**\n * Handle form validation errors\n */ const handleValidationErrors = (errors)=>{\n    const errorMessages = Object.entries(errors).map((param)=>{\n        let [field, messages] = param;\n        return \"\".concat(field, \": \").concat(messages.join(\", \"));\n    }).join(\"\\n\");\n    console.error(\"Validation errors:\", errorMessages);\n// toast({\n//   title: 'خطأ في البيانات',\n//   description: errorMessages,\n//   variant: 'destructive',\n//   duration: 7000\n// })\n};\n/**\n * Handle network errors\n */ const handleNetworkError = (error)=>{\n    var _error_response;\n    let message = \"خطأ في الشبكة\";\n    if (!navigator.onLine) {\n        message = \"لا يوجد اتصال بالإنترنت\";\n    } else if (error.code === \"ECONNABORTED\") {\n        message = \"انتهت مهلة الاتصال\";\n    } else if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) >= 500) {\n        message = \"خطأ في الخادم، يرجى المحاولة لاحقاً\";\n    }\n    console.error(\"Network error:\", message);\n// toast({\n//   title: 'مشكلة في الاتصال',\n//   description: message,\n//   variant: 'destructive',\n//   duration: 5000\n// })\n};\n/**\n * Success message handler\n */ const handleSuccess = function(message) {\n    let title = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"نجح العملية\";\n    toast({\n        title,\n        description: message,\n        variant: \"default\",\n        duration: 3000\n    });\n};\n/**\n * Warning message handler\n */ const handleWarning = function(message) {\n    let title = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"تحذير\";\n    toast({\n        title,\n        description: message,\n        variant: \"destructive\",\n        duration: 4000\n    });\n};\n/**\n * Info message handler\n */ const handleInfo = function(message) {\n    let title = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"معلومة\";\n    toast({\n        title,\n        description: message,\n        variant: \"default\",\n        duration: 3000\n    });\n};\n/**\n * Async operation wrapper with error handling\n */ const withErrorHandling = async function(operation, context) {\n    let showToast = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n    try {\n        return await operation();\n    } catch (error) {\n        handleAPIError(error, context, showToast);\n        return null;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/errorHandler.ts\n"));

/***/ })

});