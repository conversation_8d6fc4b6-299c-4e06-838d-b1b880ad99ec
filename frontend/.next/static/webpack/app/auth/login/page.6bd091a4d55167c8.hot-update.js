"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./lib/errorHandler.ts":
/*!*****************************!*\
  !*** ./lib/errorHandler.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getErrorMessage: function() { return /* binding */ getErrorMessage; },\n/* harmony export */   handleAPIError: function() { return /* binding */ handleAPIError; },\n/* harmony export */   handleInfo: function() { return /* binding */ handleInfo; },\n/* harmony export */   handleNetworkError: function() { return /* binding */ handleNetworkError; },\n/* harmony export */   handleSuccess: function() { return /* binding */ handleSuccess; },\n/* harmony export */   handleValidationErrors: function() { return /* binding */ handleValidationErrors; },\n/* harmony export */   handleWarning: function() { return /* binding */ handleWarning; },\n/* harmony export */   withErrorHandling: function() { return /* binding */ withErrorHandling; }\n/* harmony export */ });\n// import { toast } from '@/components/ui/use-toast'\n/**\n * Enhanced Error Handler for API and Application Errors\n */ /**\n * Error message translations from English to Arabic\n */ const errorTranslations = {\n    // Authentication errors\n    \"Invalid credentials\": \"البريد الإلكتروني أو كلمة المرور غير صحيحة\",\n    \"User not found\": \"المستخدم غير موجود\",\n    \"Account suspended\": \"تم تعليق الحساب\",\n    \"Account blocked\": \"تم حظر الحساب\",\n    \"Email not verified\": \"يرجى تأكيد البريد الإلكتروني\",\n    \"Token expired\": \"انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى\",\n    \"Invalid token\": \"رمز المصادقة غير صحيح\",\n    // Validation errors\n    \"Validation failed\": \"فشل في التحقق من البيانات\",\n    \"Required field missing\": \"حقل مطلوب مفقود\",\n    \"Invalid email format\": \"تنسيق البريد الإلكتروني غير صحيح\",\n    \"Password too weak\": \"كلمة المرور ضعيفة جداً\",\n    // Resource errors\n    \"Resource not found\": \"المورد المطلوب غير موجود\",\n    \"Access denied\": \"غير مسموح بالوصول\",\n    \"Insufficient permissions\": \"صلاحيات غير كافية\",\n    // Server errors\n    \"Server error\": \"خطأ في الخادم\",\n    \"Service unavailable\": \"الخدمة غير متاحة مؤقتاً\",\n    \"Database connection error\": \"خطأ في الاتصال بقاعدة البيانات\",\n    // Payment errors\n    \"Payment failed\": \"فشل في معالجة الدفع\",\n    \"Insufficient funds\": \"رصيد غير كافي\",\n    \"Invalid payment method\": \"طريقة دفع غير صحيحة\",\n    // File upload errors\n    \"File too large\": \"حجم الملف كبير جداً\",\n    \"Invalid file type\": \"نوع الملف غير مدعوم\",\n    \"Upload failed\": \"فشل في رفع الملف\",\n    // Network errors\n    \"Network error\": \"خطأ في الشبكة\",\n    \"Connection timeout\": \"انتهت مهلة الاتصال\",\n    \"No internet connection\": \"لا يوجد اتصال بالإنترنت\"\n};\n/**\n * Get user-friendly error message in Arabic\n */ const getErrorMessage = (error)=>{\n    var _error_response_data, _error_response, _error_data;\n    // If it's already an Arabic message, return as is\n    if (typeof error === \"string\" && /[\\u0600-\\u06FF]/.test(error)) {\n        return error;\n    }\n    // Extract message from different error formats\n    let message = \"\";\n    if (typeof error === \"string\") {\n        message = error;\n    } else if (error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n        message = error.response.data.message;\n    } else if (error === null || error === void 0 ? void 0 : error.message) {\n        message = error.message;\n    } else if (error === null || error === void 0 ? void 0 : (_error_data = error.data) === null || _error_data === void 0 ? void 0 : _error_data.message) {\n        message = error.data.message;\n    } else {\n        message = \"حدث خطأ غير متوقع\";\n    }\n    // Try to find translation\n    const translation = errorTranslations[message] || Object.keys(errorTranslations).find((key)=>message.toLowerCase().includes(key.toLowerCase()));\n    return translation ? errorTranslations[translation] : message;\n};\n/**\n * Handle API errors with user-friendly messages and logging\n */ const handleAPIError = function(error, context) {\n    let showToast = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n    var _error_response_data, _error_response, _error_response_data1, _error_response1;\n    const errorMessage = getErrorMessage(error);\n    const errorCode = (error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.errorCode) || (error === null || error === void 0 ? void 0 : error.code) || \"UNKNOWN_ERROR\";\n    // Log error for debugging\n    console.error(\"API Error:\", {\n        message: errorMessage,\n        errorCode,\n        context,\n        originalError: error,\n        timestamp: new Date().toISOString()\n    });\n    // Show toast notification if requested\n    if (showToast) {\n        console.error(\"Error:\", errorMessage);\n    // toast({\n    //   title: 'خطأ',\n    //   description: errorMessage,\n    //   variant: 'destructive',\n    //   duration: 5000\n    // })\n    }\n    // Return standardized error object\n    return {\n        success: false,\n        message: errorMessage,\n        errorCode,\n        validationErrors: error === null || error === void 0 ? void 0 : (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data1 = _error_response1.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.validationErrors,\n        timestamp: new Date().toISOString()\n    };\n};\n/**\n * Handle form validation errors\n */ const handleValidationErrors = (errors)=>{\n    const errorMessages = Object.entries(errors).map((param)=>{\n        let [field, messages] = param;\n        return \"\".concat(field, \": \").concat(messages.join(\", \"));\n    }).join(\"\\n\");\n    toast({\n        title: \"خطأ في البيانات\",\n        description: errorMessages,\n        variant: \"destructive\",\n        duration: 7000\n    });\n};\n/**\n * Handle network errors\n */ const handleNetworkError = (error)=>{\n    var _error_response;\n    let message = \"خطأ في الشبكة\";\n    if (!navigator.onLine) {\n        message = \"لا يوجد اتصال بالإنترنت\";\n    } else if (error.code === \"ECONNABORTED\") {\n        message = \"انتهت مهلة الاتصال\";\n    } else if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) >= 500) {\n        message = \"خطأ في الخادم، يرجى المحاولة لاحقاً\";\n    }\n    toast({\n        title: \"مشكلة في الاتصال\",\n        description: message,\n        variant: \"destructive\",\n        duration: 5000\n    });\n};\n/**\n * Success message handler\n */ const handleSuccess = function(message) {\n    let title = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"نجح العملية\";\n    toast({\n        title,\n        description: message,\n        variant: \"default\",\n        duration: 3000\n    });\n};\n/**\n * Warning message handler\n */ const handleWarning = function(message) {\n    let title = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"تحذير\";\n    toast({\n        title,\n        description: message,\n        variant: \"destructive\",\n        duration: 4000\n    });\n};\n/**\n * Info message handler\n */ const handleInfo = function(message) {\n    let title = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"معلومة\";\n    toast({\n        title,\n        description: message,\n        variant: \"default\",\n        duration: 3000\n    });\n};\n/**\n * Async operation wrapper with error handling\n */ const withErrorHandling = async function(operation, context) {\n    let showToast = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n    try {\n        return await operation();\n    } catch (error) {\n        handleAPIError(error, context, showToast);\n        return null;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/errorHandler.ts\n"));

/***/ })

});