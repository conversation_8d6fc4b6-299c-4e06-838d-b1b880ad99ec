'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useToast } from '@/hooks/use-toast'
import { LoadingCard, LoadingButton, LoadingState } from '@/components/LoadingStates'
import { useDataFetching } from '@/hooks/useAsyncOperation'
import { ValidatedInput, ValidatedSelect } from '@/components/FormComponents'
import { useFormValidation } from '@/hooks/useFormValidation'
import { z } from 'zod'
import { CreditCard, Plus, Trash2, Shield, CheckCircle } from 'lucide-react'
import api from '@/lib/api'

// Payment method validation schema
const paymentMethodSchema = z.object({
  type: z.string().min(1, 'يرجى اختيار نوع طريقة الدفع'),
  cardNumber: z.string().optional().refine((val) => {
    if (!val) return true
    const cleaned = val.replace(/\s/g, '')
    return /^\d{16}$/.test(cleaned)
  }, 'رقم البطاقة يجب أن يكون 16 رقماً'),
  expiryDate: z.string().optional().refine((val) => {
    if (!val) return true
    return /^(0[1-9]|1[0-2])\/\d{2}$/.test(val)
  }, 'تاريخ الانتهاء يجب أن يكون بصيغة MM/YY'),
  cvv: z.string().optional().refine((val) => {
    if (!val) return true
    return /^\d{3,4}$/.test(val)
  }, 'CVV يجب أن يكون 3 أو 4 أرقام'),
  holderName: z.string().min(2, 'اسم حامل البطاقة مطلوب').max(50, 'اسم حامل البطاقة طويل جداً'),
  bankName: z.string().optional()
})

type PaymentMethodFormData = z.infer<typeof paymentMethodSchema>

interface PaymentMethod {
  id: string
  type: 'mada' | 'visa' | 'mastercard' | 'apple_pay' | 'stc_pay' | 'bank_transfer'
  lastFour?: string
  expiryDate?: string
  isDefault: boolean
  holderName?: string
  bankName?: string
}

export default function PaymentMethodsPage() {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([])
  const [loading, setLoading] = useState(true)
  const [isAddingMethod, setIsAddingMethod] = useState(false)
  const [newMethod, setNewMethod] = useState({
    type: '',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    holderName: '',
    bankName: ''
  })
  const { toast } = useToast()

  useEffect(() => {
    loadPaymentMethods()
  }, [])

  const loadPaymentMethods = async () => {
    try {
      const response = await api.get('/payment/methods')
      if (response.data.success) {
        setPaymentMethods(response.data.data.methods || [])
      }
    } catch (error) {
      console.error('Error loading payment methods:', error)
      // For demo purposes, show sample data
      setPaymentMethods([
        {
          id: '1',
          type: 'mada',
          lastFour: '1234',
          expiryDate: '12/25',
          isDefault: true,
          holderName: 'أحمد محمد'
        },
        {
          id: '2',
          type: 'visa',
          lastFour: '5678',
          expiryDate: '08/26',
          isDefault: false,
          holderName: 'أحمد محمد'
        }
      ])
    } finally {
      setLoading(false)
    }
  }

  const addPaymentMethod = async () => {
    try {
      setIsAddingMethod(true)
      const response = await api.post('/payment/methods', newMethod)
      
      if (response.data.success) {
        toast({
          title: 'تم إضافة طريقة الدفع',
          description: 'تم إضافة طريقة الدفع بنجاح'
        })
        loadPaymentMethods()
        setNewMethod({
          type: '',
          cardNumber: '',
          expiryDate: '',
          cvv: '',
          holderName: '',
          bankName: ''
        })
      }
    } catch (error) {
      toast({
        title: 'خطأ في إضافة طريقة الدفع',
        description: 'حدث خطأ في إضافة طريقة الدفع',
        variant: 'destructive'
      })
    } finally {
      setIsAddingMethod(false)
    }
  }

  const removePaymentMethod = async (methodId: string) => {
    try {
      await api.delete(`/payment/methods/${methodId}`)
      toast({
        title: 'تم حذف طريقة الدفع',
        description: 'تم حذف طريقة الدفع بنجاح'
      })
      loadPaymentMethods()
    } catch (error) {
      toast({
        title: 'خطأ في حذف طريقة الدفع',
        description: 'حدث خطأ في حذف طريقة الدفع',
        variant: 'destructive'
      })
    }
  }

  const setDefaultMethod = async (methodId: string) => {
    try {
      await api.put(`/payment/methods/${methodId}/default`)
      toast({
        title: 'تم تحديث طريقة الدفع الافتراضية',
        description: 'تم تحديث طريقة الدفع الافتراضية بنجاح'
      })
      loadPaymentMethods()
    } catch (error) {
      toast({
        title: 'خطأ في التحديث',
        description: 'حدث خطأ في تحديث طريقة الدفع الافتراضية',
        variant: 'destructive'
      })
    }
  }

  const getPaymentMethodIcon = (type: string) => {
    switch (type) {
      case 'mada':
        return '💳'
      case 'visa':
        return '💳'
      case 'mastercard':
        return '💳'
      case 'apple_pay':
        return '📱'
      case 'stc_pay':
        return '📱'
      case 'bank_transfer':
        return '🏦'
      default:
        return '💳'
    }
  }

  const getPaymentMethodName = (type: string) => {
    switch (type) {
      case 'mada':
        return 'مدى'
      case 'visa':
        return 'فيزا'
      case 'mastercard':
        return 'ماستركارد'
      case 'apple_pay':
        return 'Apple Pay'
      case 'stc_pay':
        return 'STC Pay'
      case 'bank_transfer':
        return 'تحويل بنكي'
      default:
        return type
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <LoadingCard
          title="جاري تحميل طرق الدفع..."
          description="يرجى الانتظار بينما نحمل طرق الدفع الخاصة بك"
        />
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">طرق الدفع</h1>
          <p className="text-gray-600 mt-2">إدارة طرق الدفع الخاصة بك</p>
        </div>
        
        <Dialog>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              إضافة طريقة دفع
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>إضافة طريقة دفع جديدة</DialogTitle>
              <DialogDescription>
                أضف طريقة دفع جديدة لحسابك
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="type">نوع طريقة الدفع</Label>
                <Select value={newMethod.type} onValueChange={(value) => setNewMethod({...newMethod, type: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر نوع طريقة الدفع" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="mada">مدى</SelectItem>
                    <SelectItem value="visa">فيزا</SelectItem>
                    <SelectItem value="mastercard">ماستركارد</SelectItem>
                    <SelectItem value="apple_pay">Apple Pay</SelectItem>
                    <SelectItem value="stc_pay">STC Pay</SelectItem>
                    <SelectItem value="bank_transfer">تحويل بنكي</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {(newMethod.type === 'mada' || newMethod.type === 'visa' || newMethod.type === 'mastercard') && (
                <>
                  <div>
                    <Label htmlFor="cardNumber">رقم البطاقة</Label>
                    <Input
                      id="cardNumber"
                      placeholder="1234 5678 9012 3456"
                      value={newMethod.cardNumber}
                      onChange={(e) => setNewMethod({...newMethod, cardNumber: e.target.value})}
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="expiryDate">تاريخ الانتهاء</Label>
                      <Input
                        id="expiryDate"
                        placeholder="MM/YY"
                        value={newMethod.expiryDate}
                        onChange={(e) => setNewMethod({...newMethod, expiryDate: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="cvv">CVV</Label>
                      <Input
                        id="cvv"
                        placeholder="123"
                        value={newMethod.cvv}
                        onChange={(e) => setNewMethod({...newMethod, cvv: e.target.value})}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="holderName">اسم حامل البطاقة</Label>
                    <Input
                      id="holderName"
                      placeholder="الاسم كما يظهر على البطاقة"
                      value={newMethod.holderName}
                      onChange={(e) => setNewMethod({...newMethod, holderName: e.target.value})}
                    />
                  </div>
                </>
              )}

              {newMethod.type === 'bank_transfer' && (
                <div>
                  <Label htmlFor="bankName">اسم البنك</Label>
                  <Input
                    id="bankName"
                    placeholder="اسم البنك"
                    value={newMethod.bankName}
                    onChange={(e) => setNewMethod({...newMethod, bankName: e.target.value})}
                  />
                </div>
              )}

              <Button 
                onClick={addPaymentMethod} 
                disabled={isAddingMethod || !newMethod.type}
                className="w-full"
              >
                {isAddingMethod ? 'جاري الإضافة...' : 'إضافة طريقة الدفع'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-4">
        {paymentMethods.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <CreditCard className="h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold mb-2">لا توجد طرق دفع</h3>
              <p className="text-gray-600 text-center mb-4">
                لم تقم بإضافة أي طرق دفع بعد. أضف طريقة دفع للبدء في المعاملات.
              </p>
            </CardContent>
          </Card>
        ) : (
          paymentMethods.map((method) => (
            <Card key={method.id} className={method.isDefault ? 'ring-2 ring-blue-500' : ''}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">
                      {getPaymentMethodIcon(method.type)}
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-semibold">
                          {getPaymentMethodName(method.type)}
                        </h3>
                        {method.isDefault && (
                          <Badge variant="default" className="text-xs">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            افتراضي
                          </Badge>
                        )}
                      </div>
                      {method.lastFour && (
                        <p className="text-gray-600">
                          •••• •••• •••• {method.lastFour}
                        </p>
                      )}
                      {method.holderName && (
                        <p className="text-sm text-gray-500">{method.holderName}</p>
                      )}
                      {method.expiryDate && (
                        <p className="text-sm text-gray-500">ينتهي في {method.expiryDate}</p>
                      )}
                      {method.bankName && (
                        <p className="text-sm text-gray-500">{method.bankName}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {!method.isDefault && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setDefaultMethod(method.id)}
                      >
                        جعل افتراضي
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removePaymentMethod(method.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            الأمان والحماية
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm text-gray-600">
            <p>• جميع معلومات الدفع محمية بتشفير SSL 256-bit</p>
            <p>• لا نحتفظ بمعلومات البطاقة الكاملة على خوادمنا</p>
            <p>• جميع المعاملات تتم عبر بوابات دفع معتمدة</p>
            <p>• يمكنك حذف طرق الدفع في أي وقت</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
