'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react'
import { authAPI } from '@/lib/api'
import { useToast } from '@/components/ui/use-toast'
import { useFormSubmission } from '@/hooks/useAsyncOperation'
import { LoadingButton } from '@/components/LoadingStates'

const loginSchema = z.object({
  email: z.string().email('البريد الإلكتروني غير صحيح'),
  password: z.string().min(6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
})

type LoginFormData = z.infer<typeof loginSchema>

export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false)
  const router = useRouter()
  const { toast } = useToast()

  // Use enhanced form submission hook
  const { submit, isSubmitting, error, success } = useFormSubmission({
    context: { component: 'LoginPage', action: 'user_login' },
    onSuccess: (data) => {
      toast({
        title: 'تم تسجيل الدخول بنجاح',
        description: 'مرحباً بك في المنصة',
        variant: 'default'
      })
    }
  })

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  })

  const onSubmit = async (data: LoginFormData) => {
    setLoading(true)
    setError('')
    setSuccess('')

    try {
      const response = await authAPI.login(data)
      const responseData = response.data

      if (responseData.success) {
        localStorage.setItem('token', responseData.data.accessToken)
        localStorage.setItem('refreshToken', responseData.data.refreshToken)
        localStorage.setItem('user', JSON.stringify(responseData.data.user))

        setSuccess('تم تسجيل الدخول بنجاح')
        toast({
          title: 'تم تسجيل الدخول بنجاح',
          description: 'مرحباً بك في منصة المزادات والمناقصات',
          variant: 'default',
        })

        const { role, status } = responseData.data.user

        if (role === 'admin' || role === 'super_admin') {
          router.push('/admin/dashboard')
        } else if (status === 'approved') {
          router.push('/dashboard')
        } else {
          router.push('/account-status')
        }
      } else {
        setError(responseData.message || 'حدث خطأ أثناء تسجيل الدخول')
      }
    } catch (error: any) {
      let errorMessage = 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.'

      if (error.response?.data?.message) {
        const backendMessage = error.response.data.message

        // Translate common backend error messages to Arabic
        if (backendMessage.includes('verify your email') || backendMessage.includes('email address before logging')) {
          errorMessage = 'يرجى تأكيد بريدك الإلكتروني قبل تسجيل الدخول'
        } else if (backendMessage.includes('Invalid credentials') || backendMessage.includes('invalid')) {
          errorMessage = 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
        } else if (backendMessage.includes('suspended') || backendMessage.includes('blocked')) {
          errorMessage = 'تم تعليق حسابك. يرجى التواصل مع الدعم الفني'
        } else if (backendMessage.includes('User not found')) {
          errorMessage = 'المستخدم غير موجود. يرجى التحقق من البريد الإلكتروني'
        } else {
          // Use the backend message if no translation found
          errorMessage = backendMessage
        }
      }

      setError(errorMessage)
      toast({
        title: 'خطأ في تسجيل الدخول',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4">
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>
      </div>

      <div className="w-full max-w-md relative z-10">
        <div className="text-center mb-8">
          <Link href="/" className="inline-flex items-center gap-3 mb-8 group">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <Eye className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">منصة المزادات</h1>
              <p className="text-sm text-gray-600">والمناقصات</p>
            </div>
          </Link>

          <h2 className="text-3xl font-bold text-gray-900 mb-2">مرحباً بعودتك</h2>
          <p className="text-gray-600">ادخل بياناتك للوصول إلى حسابك</p>
        </div>

        <Card className="backdrop-blur-sm bg-white/80 border-0 shadow-2xl">
          <CardHeader className="text-center pb-4">
            <CardTitle className="text-2xl font-bold text-gray-900">تسجيل الدخول</CardTitle>
            <CardDescription className="text-gray-600">
              يرجى ملء البيانات التالية للمتابعة
            </CardDescription>
          </CardHeader>
          <CardContent className="p-8 pt-0">
            {error && (
              <div className="bg-gradient-to-r from-red-50 to-red-100 border border-red-200 rounded-2xl p-4 mb-6 shadow-lg">
                <div className="flex items-start">
                  <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center mr-3 mt-0.5">
                    <AlertCircle className="h-4 w-4 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className="text-red-700 font-medium">{error}</p>
                    {error.includes('تأكيد بريدك الإلكتروني') && (
                      <div className="mt-3 pt-3 border-t border-red-200">
                        <p className="text-red-600 text-sm mb-2">
                          لم تتلق رسالة التأكيد؟
                        </p>
                        <button
                          type="button"
                          className="text-sm bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                          disabled={loading}
                          onClick={async () => {
                            try {
                              setLoading(true)
                              const email = watch('email')

                              if (!email) {
                                toast({
                                  title: 'خطأ',
                                  description: 'يرجى إدخال البريد الإلكتروني أولاً',
                                  variant: 'destructive',
                                })
                                return
                              }

                              const response = await authAPI.resendVerification(email)

                              if (response.data.success) {
                                toast({
                                  title: 'تم إرسال رسالة التأكيد',
                                  description: 'يرجى التحقق من بريدك الإلكتروني',
                                  variant: 'default',
                                })
                              }
                            } catch (error: any) {
                              let errorMessage = 'حدث خطأ أثناء إرسال رسالة التأكيد'

                              if (error.response?.data?.message) {
                                const backendMessage = error.response.data.message
                                if (backendMessage.includes('already verified')) {
                                  errorMessage = 'البريد الإلكتروني مؤكد بالفعل'
                                } else if (backendMessage.includes('not found')) {
                                  errorMessage = 'البريد الإلكتروني غير موجود'
                                } else {
                                  errorMessage = backendMessage
                                }
                              }

                              toast({
                                title: 'خطأ',
                                description: errorMessage,
                                variant: 'destructive',
                              })
                            } finally {
                              setLoading(false)
                            }
                          }}
                        >
                          {loading ? 'جارٍ الإرسال...' : 'إعادة إرسال رسالة التأكيد'}
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {success && (
              <div className="bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-2xl p-4 mb-6 shadow-lg">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                    <CheckCircle className="h-4 w-4 text-white" />
                  </div>
                  <p className="text-green-700 font-medium">{success}</p>
                </div>
              </div>
            )}

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-gray-700 font-medium">البريد الإلكتروني</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  disabled={loading}
                  className="h-12 rounded-xl border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-all duration-300"
                  {...register('email')}
                />
                {errors.email && (
                  <p className="text-sm text-red-500 mt-2 flex items-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    {errors.email.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="password" className="text-gray-700 font-medium">كلمة المرور</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="ادخل كلمة المرور"
                    disabled={loading}
                    className="h-12 rounded-xl border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-all duration-300 pr-12"
                    {...register('password')}
                  />
                  <button
                    type="button"
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 p-1 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={loading}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-500" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-500" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="text-sm text-red-500 mt-2 flex items-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    {errors.password.message}
                  </p>
                )}
              </div>

              <div className="flex items-center justify-end">
                <Link
                  href="/auth/forgot-password"
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200"
                >
                  نسيت كلمة المرور؟
                </Link>
              </div>

              <LoadingButton
                type="submit"
                loading={isSubmitting}
                loadingText="جاري تسجيل الدخول..."
                className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
              >
                تسجيل الدخول
              </LoadingButton>

              <div className="mt-8 text-center">
                <p className="text-gray-600">
                  ليس لديك حساب؟{' '}
                  <Link
                    href="/auth/register"
                    className="text-blue-600 hover:text-blue-800 font-semibold transition-colors duration-200"
                  >
                    إنشاء حساب جديد
                  </Link>
                </p>
              </div>
            </form>
          </CardContent>
        </Card>

        <div className="mt-8 text-center">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-gray-600 hover:text-blue-600 font-medium transition-colors duration-200 bg-white/60 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-200 hover:border-blue-300"
          >
            ← العودة إلى الصفحة الرئيسية
          </Link>
        </div>
      </div>
    </div>
  )
}