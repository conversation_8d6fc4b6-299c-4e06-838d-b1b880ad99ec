'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Shield, 
  Smartphone, 
  QrCode, 
  Copy, 
  CheckCircle, 
  AlertTriangle,
  Download,
  Eye,
  EyeOff
} from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { LoadingButton } from '@/components/LoadingStates'
import { useAsyncOperation } from '@/hooks/useAsyncOperation'
import api from '@/lib/api'

interface TwoFactorSetupProps {
  onSetupComplete?: () => void
  onCancel?: () => void
}

export default function TwoFactorSetup({ onSetupComplete, onCancel }: TwoFactorSetupProps) {
  const [step, setStep] = useState<'method' | 'setup' | 'verify' | 'backup'>('method')
  const [selectedMethod, setSelectedMethod] = useState<'totp' | 'sms'>('totp')
  const [setupData, setSetupData] = useState<any>(null)
  const [verificationCode, setVerificationCode] = useState('')
  const [backupCodes, setBackupCodes] = useState<string[]>([])
  const [showBackupCodes, setShowBackupCodes] = useState(false)
  const { toast } = useToast()

  const { execute: setupTOTP, loading: setupLoading } = useAsyncOperation({
    onSuccess: (data) => {
      setSetupData(data)
      setStep('verify')
    }
  })

  const { execute: verifyTOTP, loading: verifyLoading } = useAsyncOperation({
    onSuccess: (data) => {
      setBackupCodes(data.backupCodes)
      setStep('backup')
      toast({
        title: 'تم تفعيل المصادقة الثنائية',
        description: 'تم تفعيل المصادقة الثنائية بنجاح'
      })
    }
  })

  const handleSetupTOTP = async () => {
    await setupTOTP(async () => {
      const response = await api.post('/2fa/setup/totp')
      return response.data.data
    })
  }

  const handleVerifyTOTP = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      toast({
        title: 'رمز غير صحيح',
        description: 'يرجى إدخال رمز مكون من 6 أرقام',
        variant: 'destructive'
      })
      return
    }

    await verifyTOTP(async () => {
      const response = await api.post('/2fa/verify/totp', {
        token: verificationCode
      })
      return response.data.data
    })
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: 'تم النسخ',
      description: 'تم نسخ النص إلى الحافظة'
    })
  }

  const downloadBackupCodes = () => {
    const content = `رموز الاحتياط للمصادقة الثنائية\n\n${backupCodes.join('\n')}\n\nاحتفظ بهذه الرموز في مكان آمن`
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'backup-codes.txt'
    a.click()
    URL.revokeObjectURL(url)
  }

  const handleComplete = () => {
    if (onSetupComplete) {
      onSetupComplete()
    }
  }

  return (
    <div className="max-w-2xl mx-auto">
      {step === 'method' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              إعداد المصادقة الثنائية
            </CardTitle>
            <CardDescription>
              اختر طريقة المصادقة الثنائية لحماية حسابك
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Tabs value={selectedMethod} onValueChange={(value) => setSelectedMethod(value as any)}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="totp">تطبيق المصادقة</TabsTrigger>
                <TabsTrigger value="sms" disabled>رسالة نصية</TabsTrigger>
              </TabsList>
              
              <TabsContent value="totp" className="space-y-4">
                <div className="flex items-start gap-3 p-4 border rounded-lg">
                  <Smartphone className="h-5 w-5 text-blue-500 mt-1" />
                  <div>
                    <h3 className="font-medium">تطبيق المصادقة (موصى به)</h3>
                    <p className="text-sm text-gray-600 mt-1">
                      استخدم تطبيق Google Authenticator أو Authy لإنشاء رموز أمان
                    </p>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="secondary">آمن</Badge>
                      <Badge variant="secondary">يعمل بدون إنترنت</Badge>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="sms" className="space-y-4">
                <div className="flex items-start gap-3 p-4 border rounded-lg opacity-50">
                  <Smartphone className="h-5 w-5 text-gray-400 mt-1" />
                  <div>
                    <h3 className="font-medium text-gray-500">رسالة نصية</h3>
                    <p className="text-sm text-gray-500 mt-1">
                      استقبال رموز الأمان عبر الرسائل النصية
                    </p>
                    <Badge variant="outline" className="mt-2">قريباً</Badge>
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            <div className="flex gap-3">
              <Button onClick={handleSetupTOTP} disabled={selectedMethod !== 'totp'}>
                متابعة
              </Button>
              {onCancel && (
                <Button variant="outline" onClick={onCancel}>
                  إلغاء
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {step === 'setup' && setupData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <QrCode className="h-5 w-5" />
              إعداد تطبيق المصادقة
            </CardTitle>
            <CardDescription>
              امسح رمز QR أو أدخل المفتاح يدوياً في تطبيق المصادقة
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-center">
              <div className="inline-block p-4 bg-white border rounded-lg">
                <img 
                  src={setupData.qrCode} 
                  alt="QR Code" 
                  className="w-48 h-48"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>أو أدخل هذا المفتاح يدوياً:</Label>
              <div className="flex items-center gap-2">
                <Input 
                  value={setupData.secret} 
                  readOnly 
                  className="font-mono text-sm"
                />
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => copyToClipboard(setupData.secret)}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                تأكد من حفظ هذا المفتاح في مكان آمن. ستحتاجه لاستعادة الوصول إذا فقدت جهازك.
              </AlertDescription>
            </Alert>

            <div className="flex gap-3">
              <Button onClick={() => setStep('verify')}>
                تم الإعداد، متابعة
              </Button>
              <Button variant="outline" onClick={() => setStep('method')}>
                رجوع
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {step === 'verify' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              تأكيد الإعداد
            </CardTitle>
            <CardDescription>
              أدخل الرمز المكون من 6 أرقام من تطبيق المصادقة
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="verification-code">رمز التحقق</Label>
              <Input
                id="verification-code"
                type="text"
                placeholder="123456"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                className="text-center text-lg font-mono tracking-widest"
                maxLength={6}
              />
            </div>

            <div className="flex gap-3">
              <LoadingButton
                loading={verifyLoading}
                onClick={handleVerifyTOTP}
                disabled={verificationCode.length !== 6}
              >
                تأكيد وتفعيل
              </LoadingButton>
              <Button variant="outline" onClick={() => setStep('setup')}>
                رجوع
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {step === 'backup' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-green-500" />
              رموز الاحتياط
            </CardTitle>
            <CardDescription>
              احفظ هذه الرموز في مكان آمن. يمكنك استخدامها للوصول إلى حسابك إذا فقدت جهازك
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>مهم جداً:</strong> هذه الرموز ستظهر مرة واحدة فقط. احفظها في مكان آمن.
              </AlertDescription>
            </Alert>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>رموز الاحتياط ({backupCodes.length} رمز)</Label>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowBackupCodes(!showBackupCodes)}
                  >
                    {showBackupCodes ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    {showBackupCodes ? 'إخفاء' : 'إظهار'}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={downloadBackupCodes}
                  >
                    <Download className="h-4 w-4" />
                    تحميل
                  </Button>
                </div>
              </div>
              
              {showBackupCodes && (
                <div className="grid grid-cols-2 gap-2 p-4 bg-gray-50 rounded-lg font-mono text-sm">
                  {backupCodes.map((code, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-white rounded border">
                      <span>{code}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(code)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="flex gap-3">
              <Button onClick={handleComplete} className="bg-green-600 hover:bg-green-700">
                تم، إنهاء الإعداد
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
