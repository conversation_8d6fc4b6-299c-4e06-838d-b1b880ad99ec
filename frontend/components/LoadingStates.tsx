'use client'

import React from 'react'
import { Loader2, RefreshCw, Download, Upload, Search, Send } from 'lucide-react'
import { cn } from '@/lib/utils'

/**
 * Loading States Components
 * Provides consistent loading indicators across the application
 */

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  className 
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12'
  }

  return (
    <Loader2 
      className={cn(
        'animate-spin text-blue-600',
        sizeClasses[size],
        className
      )} 
    />
  )
}

interface LoadingButtonProps {
  loading: boolean
  children: React.ReactNode
  loadingText?: string
  icon?: 'refresh' | 'download' | 'upload' | 'search' | 'send'
  className?: string
  disabled?: boolean
  onClick?: () => void
  type?: 'button' | 'submit'
}

export const LoadingButton: React.FC<LoadingButtonProps> = ({
  loading,
  children,
  loadingText,
  icon,
  className,
  disabled,
  onClick,
  type = 'button'
}) => {
  const getIcon = () => {
    if (!icon) return <LoadingSpinner size="sm" />
    
    const iconProps = { className: 'h-4 w-4 animate-spin' }
    
    switch (icon) {
      case 'refresh': return <RefreshCw {...iconProps} />
      case 'download': return <Download {...iconProps} />
      case 'upload': return <Upload {...iconProps} />
      case 'search': return <Search {...iconProps} />
      case 'send': return <Send {...iconProps} />
      default: return <LoadingSpinner size="sm" />
    }
  }

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={loading || disabled}
      className={cn(
        'inline-flex items-center justify-center gap-2 px-4 py-2 rounded-lg',
        'bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50',
        'disabled:cursor-not-allowed transition-colors duration-200',
        className
      )}
    >
      {loading && getIcon()}
      {loading ? (loadingText || 'جاري التحميل...') : children}
    </button>
  )
}

interface LoadingCardProps {
  title?: string
  description?: string
  className?: string
}

export const LoadingCard: React.FC<LoadingCardProps> = ({
  title = 'جاري التحميل...',
  description = 'يرجى الانتظار',
  className
}) => {
  return (
    <div className={cn(
      'flex flex-col items-center justify-center p-8 bg-white rounded-lg shadow-sm border',
      className
    )}>
      <LoadingSpinner size="lg" className="mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600 text-center">{description}</p>
    </div>
  )
}

interface LoadingOverlayProps {
  show: boolean
  message?: string
  className?: string
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  show,
  message = 'جاري التحميل...',
  className
}) => {
  if (!show) return null

  return (
    <div className={cn(
      'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50',
      className
    )}>
      <div className="bg-white rounded-lg p-6 shadow-xl">
        <div className="flex flex-col items-center">
          <LoadingSpinner size="lg" className="mb-4" />
          <p className="text-gray-900 font-medium">{message}</p>
        </div>
      </div>
    </div>
  )
}

interface SkeletonProps {
  className?: string
  lines?: number
  avatar?: boolean
}

export const Skeleton: React.FC<SkeletonProps> = ({ 
  className, 
  lines = 1,
  avatar = false 
}) => {
  return (
    <div className={cn('animate-pulse', className)}>
      {avatar && (
        <div className="w-12 h-12 bg-gray-200 rounded-full mb-4"></div>
      )}
      {Array.from({ length: lines }).map((_, index) => (
        <div 
          key={index}
          className={cn(
            'h-4 bg-gray-200 rounded mb-2',
            index === lines - 1 && 'w-3/4',
            index === 0 && lines > 1 && 'w-full'
          )}
        ></div>
      ))}
    </div>
  )
}

interface LoadingListProps {
  count?: number
  showAvatar?: boolean
  className?: string
}

export const LoadingList: React.FC<LoadingListProps> = ({
  count = 3,
  showAvatar = false,
  className
}) => {
  return (
    <div className={cn('space-y-4', className)}>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="border rounded-lg p-4">
          <Skeleton lines={3} avatar={showAvatar} />
        </div>
      ))}
    </div>
  )
}

interface LoadingTableProps {
  rows?: number
  columns?: number
  className?: string
}

export const LoadingTable: React.FC<LoadingTableProps> = ({
  rows = 5,
  columns = 4,
  className
}) => {
  return (
    <div className={cn('w-full', className)}>
      <div className="border rounded-lg overflow-hidden">
        {/* Header */}
        <div className="bg-gray-50 p-4 border-b">
          <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
            {Array.from({ length: columns }).map((_, index) => (
              <Skeleton key={index} className="h-4" />
            ))}
          </div>
        </div>
        
        {/* Rows */}
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="p-4 border-b last:border-b-0">
            <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
              {Array.from({ length: columns }).map((_, colIndex) => (
                <Skeleton key={colIndex} className="h-4" />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

interface ProgressBarProps {
  progress: number
  label?: string
  showPercentage?: boolean
  className?: string
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  label,
  showPercentage = true,
  className
}) => {
  const clampedProgress = Math.min(Math.max(progress, 0), 100)

  return (
    <div className={cn('w-full', className)}>
      {(label || showPercentage) && (
        <div className="flex justify-between items-center mb-2">
          {label && <span className="text-sm font-medium text-gray-700">{label}</span>}
          {showPercentage && (
            <span className="text-sm text-gray-500">{Math.round(clampedProgress)}%</span>
          )}
        </div>
      )}
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${clampedProgress}%` }}
        ></div>
      </div>
    </div>
  )
}

interface LoadingStateProps {
  loading: boolean
  error?: string | null
  children: React.ReactNode
  loadingComponent?: React.ReactNode
  errorComponent?: React.ReactNode
  onRetry?: () => void
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  loading,
  error,
  children,
  loadingComponent,
  errorComponent,
  onRetry
}) => {
  if (loading) {
    return loadingComponent || <LoadingCard />
  }

  if (error) {
    return errorComponent || (
      <div className="text-center p-8">
        <p className="text-red-600 mb-4">{error}</p>
        {onRetry && (
          <LoadingButton loading={false} onClick={onRetry}>
            إعادة المحاولة
          </LoadingButton>
        )}
      </div>
    )
  }

  return <>{children}</>
}

// Export all components
export default {
  LoadingSpinner,
  LoadingButton,
  LoadingCard,
  LoadingOverlay,
  Skeleton,
  LoadingList,
  LoadingTable,
  ProgressBar,
  LoadingState
}
