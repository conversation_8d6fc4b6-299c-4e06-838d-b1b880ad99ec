'use client'

import React, { forwardRef } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { CheckCircle, AlertCircle, Loader2, Eye, EyeOff } from 'lucide-react'
import { cn } from '@/lib/utils'

/**
 * Enhanced Form Components with Real-time Validation Feedback
 */

export interface FormFieldProps {
  label: string
  error?: string
  success?: boolean
  loading?: boolean
  required?: boolean
  helpText?: string
  className?: string
  showValidationIcon?: boolean
}

export interface ValidatedInputProps extends React.InputHTMLAttributes<HTMLInputElement>, FormFieldProps {
  showPasswordToggle?: boolean
}

export const ValidatedInput = forwardRef<HTMLInputElement, ValidatedInputProps>(
  ({ 
    label, 
    error, 
    success, 
    loading, 
    required, 
    helpText, 
    className,
    showValidationIcon = true,
    showPasswordToggle = false,
    type = 'text',
    ...props 
  }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false)
    const inputType = showPasswordToggle ? (showPassword ? 'text' : 'password') : type

    const getValidationIcon = () => {
      if (loading) return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      if (error) return <AlertCircle className="h-4 w-4 text-red-500" />
      if (success) return <CheckCircle className="h-4 w-4 text-green-500" />
      return null
    }

    const getInputClassName = () => {
      let baseClass = "h-12 rounded-xl border-2 transition-all duration-300 pr-4"
      
      if (error) {
        baseClass += " border-red-300 focus:border-red-500 focus:ring-red-500/20"
      } else if (success) {
        baseClass += " border-green-300 focus:border-green-500 focus:ring-green-500/20"
      } else {
        baseClass += " border-gray-200 focus:border-blue-500 focus:ring-blue-500/20"
      }

      if (showValidationIcon || showPasswordToggle) {
        baseClass += " pl-12"
      }

      return cn(baseClass, className)
    }

    return (
      <div className="space-y-2">
        <Label htmlFor={props.id} className="text-gray-700 font-medium flex items-center gap-1">
          {label}
          {required && <span className="text-red-500">*</span>}
        </Label>
        
        <div className="relative">
          <Input
            ref={ref}
            type={inputType}
            className={getInputClassName()}
            {...props}
          />
          
          {/* Validation Icon */}
          {showValidationIcon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
              {getValidationIcon()}
            </div>
          )}

          {/* Password Toggle */}
          {showPasswordToggle && (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <p className="text-sm text-red-600 flex items-center gap-2 bg-red-50 p-2 rounded-lg border border-red-200">
            <AlertCircle className="h-4 w-4 flex-shrink-0" />
            {error}
          </p>
        )}

        {/* Success Message */}
        {success && !error && (
          <p className="text-sm text-green-600 flex items-center gap-2 bg-green-50 p-2 rounded-lg border border-green-200">
            <CheckCircle className="h-4 w-4 flex-shrink-0" />
            تم التحقق بنجاح
          </p>
        )}

        {/* Help Text */}
        {helpText && !error && (
          <p className="text-sm text-gray-500">{helpText}</p>
        )}
      </div>
    )
  }
)

ValidatedInput.displayName = 'ValidatedInput'

export interface ValidatedTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement>, FormFieldProps {}

export const ValidatedTextarea = forwardRef<HTMLTextAreaElement, ValidatedTextareaProps>(
  ({ 
    label, 
    error, 
    success, 
    loading, 
    required, 
    helpText, 
    className,
    showValidationIcon = true,
    ...props 
  }, ref) => {
    const getValidationIcon = () => {
      if (loading) return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      if (error) return <AlertCircle className="h-4 w-4 text-red-500" />
      if (success) return <CheckCircle className="h-4 w-4 text-green-500" />
      return null
    }

    const getTextareaClassName = () => {
      let baseClass = "min-h-[120px] rounded-xl border-2 transition-all duration-300 resize-none"
      
      if (error) {
        baseClass += " border-red-300 focus:border-red-500 focus:ring-red-500/20"
      } else if (success) {
        baseClass += " border-green-300 focus:border-green-500 focus:ring-green-500/20"
      } else {
        baseClass += " border-gray-200 focus:border-blue-500 focus:ring-blue-500/20"
      }

      return cn(baseClass, className)
    }

    return (
      <div className="space-y-2">
        <Label htmlFor={props.id} className="text-gray-700 font-medium flex items-center gap-1">
          {label}
          {required && <span className="text-red-500">*</span>}
        </Label>
        
        <div className="relative">
          <Textarea
            ref={ref}
            className={getTextareaClassName()}
            {...props}
          />
          
          {/* Validation Icon */}
          {showValidationIcon && (
            <div className="absolute left-3 top-3">
              {getValidationIcon()}
            </div>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <p className="text-sm text-red-600 flex items-center gap-2 bg-red-50 p-2 rounded-lg border border-red-200">
            <AlertCircle className="h-4 w-4 flex-shrink-0" />
            {error}
          </p>
        )}

        {/* Success Message */}
        {success && !error && (
          <p className="text-sm text-green-600 flex items-center gap-2 bg-green-50 p-2 rounded-lg border border-green-200">
            <CheckCircle className="h-4 w-4 flex-shrink-0" />
            تم التحقق بنجاح
          </p>
        )}

        {/* Help Text */}
        {helpText && !error && (
          <p className="text-sm text-gray-500">{helpText}</p>
        )}
      </div>
    )
  }
)

ValidatedTextarea.displayName = 'ValidatedTextarea'

export interface ValidatedSelectProps extends FormFieldProps {
  options: { value: string; label: string }[]
  placeholder?: string
  value?: string
  onValueChange?: (value: string) => void
}

export const ValidatedSelect: React.FC<ValidatedSelectProps> = ({
  label,
  error,
  success,
  loading,
  required,
  helpText,
  className,
  showValidationIcon = true,
  options,
  placeholder = "اختر...",
  value,
  onValueChange
}) => {
  const getValidationIcon = () => {
    if (loading) return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
    if (error) return <AlertCircle className="h-4 w-4 text-red-500" />
    if (success) return <CheckCircle className="h-4 w-4 text-green-500" />
    return null
  }

  const getTriggerClassName = () => {
    let baseClass = "h-12 rounded-xl border-2 transition-all duration-300"
    
    if (error) {
      baseClass += " border-red-300 focus:border-red-500 focus:ring-red-500/20"
    } else if (success) {
      baseClass += " border-green-300 focus:border-green-500 focus:ring-green-500/20"
    } else {
      baseClass += " border-gray-200 focus:border-blue-500 focus:ring-blue-500/20"
    }

    return cn(baseClass, className)
  }

  return (
    <div className="space-y-2">
      <Label className="text-gray-700 font-medium flex items-center gap-1">
        {label}
        {required && <span className="text-red-500">*</span>}
      </Label>
      
      <div className="relative">
        <Select value={value} onValueChange={onValueChange}>
          <SelectTrigger className={getTriggerClassName()}>
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent>
            {options.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        {/* Validation Icon */}
        {showValidationIcon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
            {getValidationIcon()}
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <p className="text-sm text-red-600 flex items-center gap-2 bg-red-50 p-2 rounded-lg border border-red-200">
          <AlertCircle className="h-4 w-4 flex-shrink-0" />
          {error}
        </p>
      )}

      {/* Success Message */}
      {success && !error && (
        <p className="text-sm text-green-600 flex items-center gap-2 bg-green-50 p-2 rounded-lg border border-green-200">
          <CheckCircle className="h-4 w-4 flex-shrink-0" />
          تم التحقق بنجاح
        </p>
      )}

      {/* Help Text */}
      {helpText && !error && (
        <p className="text-sm text-gray-500">{helpText}</p>
      )}
    </div>
  )
}

export interface FormSectionProps {
  title: string
  description?: string
  children: React.ReactNode
  className?: string
}

export const FormSection: React.FC<FormSectionProps> = ({
  title,
  description,
  children,
  className
}) => {
  return (
    <div className={cn("space-y-6", className)}>
      <div className="border-b border-gray-200 pb-4">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        {description && (
          <p className="text-sm text-gray-600 mt-1">{description}</p>
        )}
      </div>
      <div className="space-y-4">
        {children}
      </div>
    </div>
  )
}

export interface FormSummaryProps {
  isValid: boolean
  errorCount: number
  touchedCount: number
  totalFields: number
}

export const FormSummary: React.FC<FormSummaryProps> = ({
  isValid,
  errorCount,
  touchedCount,
  totalFields
}) => {
  const completionPercentage = Math.round((touchedCount / totalFields) * 100)

  return (
    <div className="bg-gray-50 rounded-lg p-4 border">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium text-gray-700">حالة النموذج</span>
        <span className="text-sm text-gray-500">{completionPercentage}% مكتمل</span>
      </div>
      
      <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
        <div 
          className={cn(
            "h-2 rounded-full transition-all duration-300",
            isValid ? "bg-green-500" : errorCount > 0 ? "bg-red-500" : "bg-blue-500"
          )}
          style={{ width: `${completionPercentage}%` }}
        />
      </div>

      <div className="flex items-center justify-between text-sm">
        <div className="flex items-center gap-4">
          {isValid ? (
            <span className="text-green-600 flex items-center gap-1">
              <CheckCircle className="h-4 w-4" />
              جاهز للإرسال
            </span>
          ) : (
            <span className="text-red-600 flex items-center gap-1">
              <AlertCircle className="h-4 w-4" />
              {errorCount} خطأ
            </span>
          )}
        </div>
        <span className="text-gray-500">
          {touchedCount} من {totalFields} حقل
        </span>
      </div>
    </div>
  )
}
