'use client'

import React, { useState, useEffect } from 'react'
import { useCurrency } from '@/contexts/CurrencyContext'
import { safeDisplay } from '@/lib/dataSanitizer'

interface CurrencyDisplayProps {
  amount: number
  fromCurrency: string
  className?: string
  showOriginal?: boolean
  showDualCurrency?: boolean
}

export function CurrencyDisplay({
  amount,
  fromCurrency,
  className = '',
  showOriginal = false,
  showDualCurrency = true
}: CurrencyDisplayProps) {
  const { formatAmountWithConversion, userCurrency, getCurrencySymbol } = useCurrency()
  const [displayText, setDisplayText] = useState('')
  const [usdDisplayText, setUsdDisplayText] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  // Validate and sanitize amount
  const sanitizedAmount = React.useMemo(() => {
    if (amount === null || amount === undefined || isNaN(amount) || !isFinite(amount)) {
      return 0
    }
    return Math.max(0, Number(amount))
  }, [amount])

  useEffect(() => {
    const convertAndFormat = async () => {
      try {
        setIsLoading(true)
        setHasError(false)

        // Use sanitized amount for conversion
        const amountToConvert = sanitizedAmount

        // Convert to user's selected currency
        const formattedAmount = await formatAmountWithConversion(amountToConvert, fromCurrency, userCurrency)
        setDisplayText(formattedAmount)

        // Always show USD as secondary currency if user currency is not USD and showDualCurrency is true
        if (showDualCurrency && userCurrency !== 'USD') {
          const usdFormattedAmount = await formatAmountWithConversion(amountToConvert, fromCurrency, 'USD')
          setUsdDisplayText(usdFormattedAmount)
        } else {
          setUsdDisplayText('')
        }
      } catch (error) {
        console.error('Currency conversion failed:', error)
        setHasError(true)

        // Enhanced fallback with proper error handling
        const fallbackSymbol = getCurrencySymbol(userCurrency)
        setDisplayText(`${sanitizedAmount.toLocaleString('ar-SA')} ${fallbackSymbol}`)

        // Show USD fallback if needed
        if (showDualCurrency && userCurrency !== 'USD') {
          setUsdDisplayText(`$${sanitizedAmount.toLocaleString('en-US')}`)
        } else {
          setUsdDisplayText('')
        }
      } finally {
        setIsLoading(false)
      }
    }

    convertAndFormat()
  }, [sanitizedAmount, fromCurrency, userCurrency, formatAmountWithConversion, showDualCurrency, getCurrencySymbol])

  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-4 bg-gray-200 rounded w-20"></div>
      </div>
    )
  }

  return (
    <div className={className}>
      <div className="font-semibold">
        {displayText}
        {hasError && (
          <span className="text-xs text-orange-500 ml-2" title="تقدير تقريبي - فشل في تحديث أسعار الصرف">
            *
          </span>
        )}
      </div>
      {showDualCurrency && userCurrency !== 'USD' && usdDisplayText && (
        <div className="text-sm text-gray-500 mt-1">
          <span className="text-xs text-gray-400">USD: </span>
          {usdDisplayText}
          {hasError && (
            <span className="text-xs text-orange-400 ml-1" title="تقدير تقريبي">*</span>
          )}
        </div>
      )}
      {showOriginal && fromCurrency !== userCurrency && (
        <div className="text-xs text-gray-400 mt-1">
          (من {sanitizedAmount.toLocaleString('ar-SA')} {fromCurrency})
        </div>
      )}
      {hasError && (
        <div className="text-xs text-orange-500 mt-1">
          * أسعار تقديرية - يرجى تحديث الصفحة
        </div>
      )}
    </div>
  )
}
