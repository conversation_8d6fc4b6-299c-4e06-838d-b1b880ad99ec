/**
 * Currency Utilities
 * Provides validation, sanitization, and formatting utilities for currency operations
 */

export interface CurrencyValidationResult {
  isValid: boolean
  sanitizedAmount: number
  errors: string[]
}

export interface CurrencyFormatOptions {
  showSymbol?: boolean
  showCode?: boolean
  locale?: string
  minimumFractionDigits?: number
  maximumFractionDigits?: number
}

/**
 * Validate and sanitize currency amount
 */
export function validateCurrencyAmount(amount: any): CurrencyValidationResult {
  const errors: string[] = []
  let sanitizedAmount = 0

  // Check for null/undefined
  if (amount === null || amount === undefined) {
    errors.push('المبلغ مطلوب')
    return { isValid: false, sanitizedAmount: 0, errors }
  }

  // Convert to number
  const numericAmount = Number(amount)

  // Check for NaN
  if (isNaN(numericAmount)) {
    errors.push('المبلغ يجب أن يكون رقماً صحيحاً')
    return { isValid: false, sanitizedAmount: 0, errors }
  }

  // Check for infinite values
  if (!isFinite(numericAmount)) {
    errors.push('المبلغ غير صحيح')
    return { isValid: false, sanitizedAmount: 0, errors }
  }

  // Check for negative values
  if (numericAmount < 0) {
    errors.push('المبلغ يجب أن يكون موجباً')
    return { isValid: false, sanitizedAmount: 0, errors }
  }

  // Check for extremely large values
  if (numericAmount > Number.MAX_SAFE_INTEGER) {
    errors.push('المبلغ كبير جداً')
    return { isValid: false, sanitizedAmount: 0, errors }
  }

  // Sanitize amount (round to 2 decimal places)
  sanitizedAmount = Math.round(numericAmount * 100) / 100

  return { isValid: true, sanitizedAmount, errors: [] }
}

/**
 * Validate currency code
 */
export function validateCurrencyCode(currency: string): boolean {
  const supportedCurrencies = ['USD', 'SAR', 'EUR', 'GBP', 'AED', 'KWD', 'QAR', 'BHD', 'OMR', 'JOD']
  return supportedCurrencies.includes(currency)
}

/**
 * Get currency symbol
 */
export function getCurrencySymbol(currency: string): string {
  const symbols: { [key: string]: string } = {
    USD: '$',
    SAR: 'ر.س',
    EUR: '€',
    GBP: '£',
    AED: 'د.إ',
    KWD: 'د.ك',
    QAR: 'ر.ق',
    BHD: 'د.ب',
    OMR: 'ر.ع',
    JOD: 'د.أ'
  }
  return symbols[currency] || currency
}

/**
 * Format currency amount with enhanced options
 */
export function formatCurrencyAmount(
  amount: number,
  currency: string,
  options: CurrencyFormatOptions = {}
): string {
  const {
    showSymbol = true,
    showCode = false,
    locale = 'ar-SA',
    minimumFractionDigits = 0,
    maximumFractionDigits = 2
  } = options

  // Validate and sanitize amount
  const validation = validateCurrencyAmount(amount)
  if (!validation.isValid) {
    return '0'
  }

  const sanitizedAmount = validation.sanitizedAmount
  const symbol = getCurrencySymbol(currency)

  try {
    // Format number with specified locale
    const formattedNumber = sanitizedAmount.toLocaleString(locale, {
      minimumFractionDigits,
      maximumFractionDigits
    })

    // Build display string
    let result = formattedNumber

    if (showSymbol) {
      // For Arabic currencies, put symbol after number
      if (['SAR', 'AED', 'KWD', 'QAR', 'BHD', 'OMR', 'JOD'].includes(currency)) {
        result = `${formattedNumber} ${symbol}`
      } else {
        // For Western currencies, put symbol before number
        result = `${symbol}${formattedNumber}`
      }
    }

    if (showCode) {
      result = `${result} ${currency}`
    }

    return result
  } catch (error) {
    console.error('Error formatting currency:', error)
    return `${sanitizedAmount} ${currency}`
  }
}

/**
 * Parse currency string to number
 */
export function parseCurrencyString(currencyString: string): number {
  if (!currencyString || typeof currencyString !== 'string') {
    return 0
  }

  // Remove currency symbols and codes
  const cleanString = currencyString
    .replace(/[^\d.,\-]/g, '') // Keep only digits, commas, dots, and minus
    .replace(/,/g, '') // Remove thousands separators
    .trim()

  const parsed = parseFloat(cleanString)
  return isNaN(parsed) ? 0 : Math.max(0, parsed)
}

/**
 * Compare two currency amounts
 */
export function compareCurrencyAmounts(amount1: number, amount2: number): number {
  const validation1 = validateCurrencyAmount(amount1)
  const validation2 = validateCurrencyAmount(amount2)

  const sanitized1 = validation1.sanitizedAmount
  const sanitized2 = validation2.sanitizedAmount

  if (sanitized1 < sanitized2) return -1
  if (sanitized1 > sanitized2) return 1
  return 0
}

/**
 * Calculate percentage difference between amounts
 */
export function calculatePercentageDifference(oldAmount: number, newAmount: number): number {
  const validation1 = validateCurrencyAmount(oldAmount)
  const validation2 = validateCurrencyAmount(newAmount)

  if (!validation1.isValid || !validation2.isValid || validation1.sanitizedAmount === 0) {
    return 0
  }

  const difference = validation2.sanitizedAmount - validation1.sanitizedAmount
  const percentage = (difference / validation1.sanitizedAmount) * 100

  return Math.round(percentage * 100) / 100 // Round to 2 decimal places
}

/**
 * Format percentage with proper Arabic display
 */
export function formatPercentage(percentage: number): string {
  const validation = validateCurrencyAmount(percentage)
  if (!validation.isValid) {
    return '0%'
  }

  const sanitized = validation.sanitizedAmount
  return `${sanitized.toLocaleString('ar-SA', { 
    minimumFractionDigits: 0, 
    maximumFractionDigits: 1 
  })}%`
}

/**
 * Get currency display name in Arabic
 */
export function getCurrencyDisplayName(currency: string): string {
  const names: { [key: string]: string } = {
    USD: 'دولار أمريكي',
    SAR: 'ريال سعودي',
    EUR: 'يورو',
    GBP: 'جنيه إسترليني',
    AED: 'درهم إماراتي',
    KWD: 'دينار كويتي',
    QAR: 'ريال قطري',
    BHD: 'دينار بحريني',
    OMR: 'ريال عماني',
    JOD: 'دينار أردني'
  }
  return names[currency] || currency
}

/**
 * Check if amount is within reasonable bounds
 */
export function isReasonableAmount(amount: number, maxAmount: number = 1000000000): boolean {
  const validation = validateCurrencyAmount(amount)
  if (!validation.isValid) {
    return false
  }

  return validation.sanitizedAmount <= maxAmount
}

/**
 * Round amount to currency precision
 */
export function roundToCurrencyPrecision(amount: number, currency: string = 'SAR'): number {
  const validation = validateCurrencyAmount(amount)
  if (!validation.isValid) {
    return 0
  }

  // Most currencies use 2 decimal places
  // Some currencies like JPY use 0 decimal places, but we don't support them yet
  return Math.round(validation.sanitizedAmount * 100) / 100
}

/**
 * Safe currency conversion with validation
 */
export function safeCurrencyConversion(
  amount: number,
  rate: number,
  fromCurrency: string,
  toCurrency: string
): { success: boolean; result: number; error?: string } {
  // Validate amount
  const amountValidation = validateCurrencyAmount(amount)
  if (!amountValidation.isValid) {
    return {
      success: false,
      result: 0,
      error: `Invalid amount: ${amountValidation.errors.join(', ')}`
    }
  }

  // Validate rate
  const rateValidation = validateCurrencyAmount(rate)
  if (!rateValidation.isValid || rateValidation.sanitizedAmount <= 0) {
    return {
      success: false,
      result: 0,
      error: 'Invalid exchange rate'
    }
  }

  // Validate currencies
  if (!validateCurrencyCode(fromCurrency) || !validateCurrencyCode(toCurrency)) {
    return {
      success: false,
      result: 0,
      error: 'Invalid currency code'
    }
  }

  try {
    const converted = amountValidation.sanitizedAmount * rateValidation.sanitizedAmount
    const rounded = roundToCurrencyPrecision(converted, toCurrency)

    return {
      success: true,
      result: rounded
    }
  } catch (error) {
    return {
      success: false,
      result: 0,
      error: 'Conversion calculation failed'
    }
  }
}
