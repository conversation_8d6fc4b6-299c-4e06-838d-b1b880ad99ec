// import { toast } from '@/components/ui/use-toast'

/**
 * Enhanced Error Handler for API and Application Errors
 */

export interface APIError {
  success: false
  message: string
  errorCode?: string
  validationErrors?: string[]
  timestamp?: string
}

export interface ErrorContext {
  action?: string
  component?: string
  userId?: string
  additionalInfo?: Record<string, any>
}

/**
 * Error message translations from English to Arabic
 */
const errorTranslations: Record<string, string> = {
  // Authentication errors
  'Invalid credentials': 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
  'User not found': 'المستخدم غير موجود',
  'Account suspended': 'تم تعليق الحساب',
  'Account blocked': 'تم حظر الحساب',
  'Email not verified': 'يرجى تأكيد البريد الإلكتروني',
  'Token expired': 'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى',
  'Invalid token': 'رمز المصادقة غير صحيح',
  
  // Validation errors
  'Validation failed': 'فشل في التحقق من البيانات',
  'Required field missing': 'حقل مطلوب مفقود',
  'Invalid email format': 'تنسيق البريد الإلكتروني غير صحيح',
  'Password too weak': 'كلمة المرور ضعيفة جداً',
  
  // Resource errors
  'Resource not found': 'المورد المطلوب غير موجود',
  'Access denied': 'غير مسموح بالوصول',
  'Insufficient permissions': 'صلاحيات غير كافية',
  
  // Server errors
  'Server error': 'خطأ في الخادم',
  'Service unavailable': 'الخدمة غير متاحة مؤقتاً',
  'Database connection error': 'خطأ في الاتصال بقاعدة البيانات',
  
  // Payment errors
  'Payment failed': 'فشل في معالجة الدفع',
  'Insufficient funds': 'رصيد غير كافي',
  'Invalid payment method': 'طريقة دفع غير صحيحة',
  
  // File upload errors
  'File too large': 'حجم الملف كبير جداً',
  'Invalid file type': 'نوع الملف غير مدعوم',
  'Upload failed': 'فشل في رفع الملف',
  
  // Network errors
  'Network error': 'خطأ في الشبكة',
  'Connection timeout': 'انتهت مهلة الاتصال',
  'No internet connection': 'لا يوجد اتصال بالإنترنت'
}

/**
 * Get user-friendly error message in Arabic
 */
export const getErrorMessage = (error: any): string => {
  // If it's already an Arabic message, return as is
  if (typeof error === 'string' && /[\u0600-\u06FF]/.test(error)) {
    return error
  }

  // Extract message from different error formats
  let message = ''
  
  if (typeof error === 'string') {
    message = error
  } else if (error?.response?.data?.message) {
    message = error.response.data.message
  } else if (error?.message) {
    message = error.message
  } else if (error?.data?.message) {
    message = error.data.message
  } else {
    message = 'حدث خطأ غير متوقع'
  }

  // Try to find translation
  const translation = errorTranslations[message] || 
                     Object.keys(errorTranslations).find(key => 
                       message.toLowerCase().includes(key.toLowerCase())
                     )

  return translation ? errorTranslations[translation] : message
}

/**
 * Handle API errors with user-friendly messages and logging
 */
export const handleAPIError = (
  error: any, 
  context?: ErrorContext,
  showToast: boolean = true
): APIError => {
  const errorMessage = getErrorMessage(error)
  const errorCode = error?.response?.data?.errorCode || error?.code || 'UNKNOWN_ERROR'
  
  // Log error for debugging
  console.error('API Error:', {
    message: errorMessage,
    errorCode,
    context,
    originalError: error,
    timestamp: new Date().toISOString()
  })

  // Show toast notification if requested
  if (showToast) {
    console.error('Error:', errorMessage)
    // toast({
    //   title: 'خطأ',
    //   description: errorMessage,
    //   variant: 'destructive',
    //   duration: 5000
    // })
  }

  // Return standardized error object
  return {
    success: false,
    message: errorMessage,
    errorCode,
    validationErrors: error?.response?.data?.validationErrors,
    timestamp: new Date().toISOString()
  }
}

/**
 * Handle form validation errors
 */
export const handleValidationErrors = (errors: Record<string, string[]>): void => {
  const errorMessages = Object.entries(errors)
    .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
    .join('\n')

  console.error('Validation errors:', errorMessages)
  // toast({
  //   title: 'خطأ في البيانات',
  //   description: errorMessages,
  //   variant: 'destructive',
  //   duration: 7000
  // })
}

/**
 * Handle network errors
 */
export const handleNetworkError = (error: any): void => {
  let message = 'خطأ في الشبكة'

  if (!navigator.onLine) {
    message = 'لا يوجد اتصال بالإنترنت'
  } else if (error.code === 'ECONNABORTED') {
    message = 'انتهت مهلة الاتصال'
  } else if (error.response?.status >= 500) {
    message = 'خطأ في الخادم، يرجى المحاولة لاحقاً'
  }

  console.error('Network error:', message)
  // toast({
  //   title: 'مشكلة في الاتصال',
  //   description: message,
  //   variant: 'destructive',
  //   duration: 5000
  // })
}

/**
 * Success message handler
 */
export const handleSuccess = (message: string, title: string = 'نجح العملية'): void => {
  console.log('Success:', title, message)
  // toast({
  //   title,
  //   description: message,
  //   variant: 'default',
  //   duration: 3000
  // })
}

/**
 * Warning message handler
 */
export const handleWarning = (message: string, title: string = 'تحذير'): void => {
  console.warn('Warning:', title, message)
  // toast({
  //   title,
  //   description: message,
  //   variant: 'destructive',
  //   duration: 4000
  // })
}

/**
 * Info message handler
 */
export const handleInfo = (message: string, title: string = 'معلومة'): void => {
  console.info('Info:', title, message)
  // toast({
  //   title,
  //   description: message,
  //   variant: 'default',
  //   duration: 3000
  // })
}

/**
 * Async operation wrapper with error handling
 */
export const withErrorHandling = async <T>(
  operation: () => Promise<T>,
  context?: ErrorContext,
  showToast: boolean = true
): Promise<T | null> => {
  try {
    return await operation()
  } catch (error) {
    handleAPIError(error, context, showToast)
    return null
  }
}

/**
 * Error boundary fallback component props
 */
export interface ErrorFallbackProps {
  error: Error
  resetError: () => void
}
