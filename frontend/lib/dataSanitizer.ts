/**
 * Data Sanitization and Integrity Utilities
 * Provides comprehensive data validation, sanitization, and fallback mechanisms
 */

export interface SanitizationOptions {
  allowNull?: boolean
  allowEmpty?: boolean
  defaultValue?: any
  maxLength?: number
  minLength?: number
  pattern?: RegExp
  transform?: (value: any) => any
}

export interface SanitizedData<T> {
  value: T
  isValid: boolean
  wasModified: boolean
  errors: string[]
  warnings: string[]
}

/**
 * Sanitize and validate any data value
 */
export function sanitizeData<T>(
  data: any,
  type: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'date',
  options: SanitizationOptions = {}
): SanitizedData<T> {
  const result: SanitizedData<T> = {
    value: data,
    isValid: true,
    wasModified: false,
    errors: [],
    warnings: []
  }

  try {
    switch (type) {
      case 'string':
        return sanitizeString(data, options) as SanitizedData<T>
      case 'number':
        return sanitizeNumber(data, options) as SanitizedData<T>
      case 'boolean':
        return sanitizeBoolean(data, options) as SanitizedData<T>
      case 'array':
        return sanitizeArray(data, options) as SanitizedData<T>
      case 'object':
        return sanitizeObject(data, options) as SanitizedData<T>
      case 'date':
        return sanitizeDate(data, options) as SanitizedData<T>
      default:
        result.errors.push(`Unsupported data type: ${type}`)
        result.isValid = false
        return result
    }
  } catch (error) {
    result.errors.push(`Sanitization error: ${error}`)
    result.isValid = false
    result.value = options.defaultValue as T
    result.wasModified = true
    return result
  }
}

/**
 * Sanitize string values
 */
function sanitizeString(data: any, options: SanitizationOptions): SanitizedData<string> {
  const result: SanitizedData<string> = {
    value: '',
    isValid: true,
    wasModified: false,
    errors: [],
    warnings: []
  }

  // Handle null/undefined
  if (data === null || data === undefined) {
    if (options.allowNull) {
      result.value = data
      return result
    }
    result.value = options.defaultValue || ''
    result.wasModified = true
    if (!options.allowEmpty && result.value === '') {
      result.errors.push('String value is required')
      result.isValid = false
    }
    return result
  }

  // Convert to string
  let stringValue = String(data).trim()

  // Apply transformation
  if (options.transform) {
    const transformed = options.transform(stringValue)
    if (transformed !== stringValue) {
      stringValue = transformed
      result.wasModified = true
    }
  }

  // Check length constraints
  if (options.minLength && stringValue.length < options.minLength) {
    result.errors.push(`String must be at least ${options.minLength} characters`)
    result.isValid = false
  }

  if (options.maxLength && stringValue.length > options.maxLength) {
    stringValue = stringValue.substring(0, options.maxLength)
    result.wasModified = true
    result.warnings.push(`String was truncated to ${options.maxLength} characters`)
  }

  // Check pattern
  if (options.pattern && !options.pattern.test(stringValue)) {
    result.errors.push('String does not match required pattern')
    result.isValid = false
  }

  // Check empty
  if (!options.allowEmpty && stringValue === '') {
    result.errors.push('String cannot be empty')
    result.isValid = false
  }

  result.value = stringValue
  return result
}

/**
 * Sanitize number values
 */
function sanitizeNumber(data: any, options: SanitizationOptions): SanitizedData<number> {
  const result: SanitizedData<number> = {
    value: 0,
    isValid: true,
    wasModified: false,
    errors: [],
    warnings: []
  }

  // Handle null/undefined
  if (data === null || data === undefined) {
    if (options.allowNull) {
      result.value = data
      return result
    }
    result.value = options.defaultValue || 0
    result.wasModified = true
    return result
  }

  // Convert to number
  let numberValue = Number(data)

  // Check for NaN
  if (isNaN(numberValue)) {
    result.errors.push('Value is not a valid number')
    result.isValid = false
    result.value = options.defaultValue || 0
    result.wasModified = true
    return result
  }

  // Check for infinite values
  if (!isFinite(numberValue)) {
    result.errors.push('Number value is infinite')
    result.isValid = false
    result.value = options.defaultValue || 0
    result.wasModified = true
    return result
  }

  // Apply transformation
  if (options.transform) {
    const transformed = options.transform(numberValue)
    if (transformed !== numberValue) {
      numberValue = transformed
      result.wasModified = true
    }
  }

  result.value = numberValue
  return result
}

/**
 * Sanitize boolean values
 */
function sanitizeBoolean(data: any, options: SanitizationOptions): SanitizedData<boolean> {
  const result: SanitizedData<boolean> = {
    value: false,
    isValid: true,
    wasModified: false,
    errors: [],
    warnings: []
  }

  // Handle null/undefined
  if (data === null || data === undefined) {
    if (options.allowNull) {
      result.value = data
      return result
    }
    result.value = options.defaultValue || false
    result.wasModified = true
    return result
  }

  // Convert to boolean
  let booleanValue: boolean

  if (typeof data === 'boolean') {
    booleanValue = data
  } else if (typeof data === 'string') {
    const lowerData = data.toLowerCase().trim()
    if (['true', '1', 'yes', 'on', 'نعم'].includes(lowerData)) {
      booleanValue = true
    } else if (['false', '0', 'no', 'off', 'لا'].includes(lowerData)) {
      booleanValue = false
    } else {
      result.errors.push('Invalid boolean value')
      result.isValid = false
      result.value = options.defaultValue || false
      result.wasModified = true
      return result
    }
  } else if (typeof data === 'number') {
    booleanValue = data !== 0
  } else {
    booleanValue = Boolean(data)
    result.wasModified = true
  }

  result.value = booleanValue
  return result
}

/**
 * Sanitize array values
 */
function sanitizeArray(data: any, options: SanitizationOptions): SanitizedData<any[]> {
  const result: SanitizedData<any[]> = {
    value: [],
    isValid: true,
    wasModified: false,
    errors: [],
    warnings: []
  }

  // Handle null/undefined
  if (data === null || data === undefined) {
    if (options.allowNull) {
      result.value = data
      return result
    }
    result.value = options.defaultValue || []
    result.wasModified = true
    return result
  }

  // Ensure it's an array
  let arrayValue: any[]
  if (Array.isArray(data)) {
    arrayValue = data
  } else {
    arrayValue = [data]
    result.wasModified = true
    result.warnings.push('Non-array value was converted to array')
  }

  // Apply transformation
  if (options.transform) {
    const transformed = options.transform(arrayValue)
    if (transformed !== arrayValue) {
      arrayValue = transformed
      result.wasModified = true
    }
  }

  result.value = arrayValue
  return result
}

/**
 * Sanitize object values
 */
function sanitizeObject(data: any, options: SanitizationOptions): SanitizedData<object> {
  const result: SanitizedData<object> = {
    value: {},
    isValid: true,
    wasModified: false,
    errors: [],
    warnings: []
  }

  // Handle null/undefined
  if (data === null || data === undefined) {
    if (options.allowNull) {
      result.value = data
      return result
    }
    result.value = options.defaultValue || {}
    result.wasModified = true
    return result
  }

  // Ensure it's an object
  let objectValue: object
  if (typeof data === 'object' && !Array.isArray(data)) {
    objectValue = data
  } else {
    result.errors.push('Value is not a valid object')
    result.isValid = false
    result.value = options.defaultValue || {}
    result.wasModified = true
    return result
  }

  // Apply transformation
  if (options.transform) {
    const transformed = options.transform(objectValue)
    if (transformed !== objectValue) {
      objectValue = transformed
      result.wasModified = true
    }
  }

  result.value = objectValue
  return result
}

/**
 * Sanitize date values
 */
function sanitizeDate(data: any, options: SanitizationOptions): SanitizedData<Date> {
  const result: SanitizedData<Date> = {
    value: new Date(),
    isValid: true,
    wasModified: false,
    errors: [],
    warnings: []
  }

  // Handle null/undefined
  if (data === null || data === undefined) {
    if (options.allowNull) {
      result.value = data
      return result
    }
    result.value = options.defaultValue || new Date()
    result.wasModified = true
    return result
  }

  // Convert to date
  let dateValue: Date
  if (data instanceof Date) {
    dateValue = data
  } else {
    dateValue = new Date(data)
  }

  // Check for invalid date
  if (isNaN(dateValue.getTime())) {
    result.errors.push('Invalid date value')
    result.isValid = false
    result.value = options.defaultValue || new Date()
    result.wasModified = true
    return result
  }

  result.value = dateValue
  return result
}

/**
 * Sanitize API response data
 */
export function sanitizeAPIResponse(response: any): any {
  if (!response || typeof response !== 'object') {
    return {
      success: false,
      message: 'Invalid API response',
      data: null
    }
  }

  return {
    success: sanitizeData(response.success, 'boolean', { defaultValue: false }).value,
    message: sanitizeData(response.message, 'string', { defaultValue: 'Unknown response' }).value,
    data: response.data || null,
    errors: sanitizeData(response.errors, 'array', { defaultValue: [] }).value,
    meta: response.meta || {}
  }
}

/**
 * Sanitize form data
 */
export function sanitizeFormData(formData: Record<string, any>): Record<string, any> {
  const sanitized: Record<string, any> = {}

  for (const [key, value] of Object.entries(formData)) {
    if (typeof value === 'string') {
      sanitized[key] = sanitizeData(value, 'string', { 
        allowEmpty: false,
        transform: (v) => v.trim()
      }).value
    } else if (typeof value === 'number') {
      sanitized[key] = sanitizeData(value, 'number', {
        defaultValue: 0
      }).value
    } else {
      sanitized[key] = value
    }
  }

  return sanitized
}

/**
 * Create safe display value with fallback
 */
export function safeDisplay(value: any, fallback: string = 'غير متوفر'): string {
  if (value === null || value === undefined) {
    return fallback
  }

  if (typeof value === 'string') {
    return value.trim() || fallback
  }

  if (typeof value === 'number') {
    if (isNaN(value) || !isFinite(value)) {
      return fallback
    }
    return value.toString()
  }

  if (typeof value === 'boolean') {
    return value ? 'نعم' : 'لا'
  }

  if (value instanceof Date) {
    if (isNaN(value.getTime())) {
      return fallback
    }
    return value.toLocaleDateString('ar-SA')
  }

  return String(value) || fallback
}
