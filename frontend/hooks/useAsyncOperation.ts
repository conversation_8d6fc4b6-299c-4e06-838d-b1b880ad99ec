'use client'

import { useState, useCallback } from 'react'
import { handleAPIError, ErrorContext } from '@/lib/errorHandler'

/**
 * Enhanced hook for managing async operations with loading states and error handling
 */

export interface AsyncOperationState<T> {
  data: T | null
  loading: boolean
  error: string | null
  success: boolean
}

export interface UseAsyncOperationOptions {
  showToast?: boolean
  context?: ErrorContext
  onSuccess?: (data: any) => void
  onError?: (error: any) => void
}

export function useAsyncOperation<T = any>(
  options: UseAsyncOperationOptions = {}
) {
  const [state, setState] = useState<AsyncOperationState<T>>({
    data: null,
    loading: false,
    error: null,
    success: false
  })

  const execute = useCallback(async (
    operation: () => Promise<T>,
    operationContext?: ErrorContext
  ): Promise<T | null> => {
    setState(prev => ({
      ...prev,
      loading: true,
      error: null,
      success: false
    }))

    try {
      const result = await operation()
      
      setState(prev => ({
        ...prev,
        data: result,
        loading: false,
        success: true
      }))

      // Call success callback if provided
      if (options.onSuccess) {
        options.onSuccess(result)
      }

      return result
    } catch (error) {
      const errorInfo = handleAPIError(
        error, 
        operationContext || options.context,
        options.showToast !== false
      )

      setState(prev => ({
        ...prev,
        loading: false,
        error: errorInfo.message,
        success: false
      }))

      // Call error callback if provided
      if (options.onError) {
        options.onError(error)
      }

      return null
    }
  }, [options])

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      success: false
    })
  }, [])

  const setData = useCallback((data: T) => {
    setState(prev => ({
      ...prev,
      data,
      success: true
    }))
  }, [])

  return {
    ...state,
    execute,
    reset,
    setData
  }
}

/**
 * Hook for managing form submissions with loading states
 */
export function useFormSubmission<T = any>(
  options: UseAsyncOperationOptions = {}
) {
  const asyncOp = useAsyncOperation<T>(options)

  const submit = useCallback(async (
    formData: any,
    submitOperation: (data: any) => Promise<T>
  ): Promise<T | null> => {
    return asyncOp.execute(
      () => submitOperation(formData),
      { ...options.context, action: 'form_submission' }
    )
  }, [asyncOp, options.context])

  return {
    ...asyncOp,
    submit,
    isSubmitting: asyncOp.loading
  }
}

/**
 * Hook for managing data fetching with loading states
 */
export function useDataFetching<T = any>(
  options: UseAsyncOperationOptions = {}
) {
  const asyncOp = useAsyncOperation<T>(options)

  const fetch = useCallback(async (
    fetchOperation: () => Promise<T>
  ): Promise<T | null> => {
    return asyncOp.execute(
      fetchOperation,
      { ...options.context, action: 'data_fetch' }
    )
  }, [asyncOp, options.context])

  const refetch = useCallback(async (
    fetchOperation: () => Promise<T>
  ): Promise<T | null> => {
    return fetch(fetchOperation)
  }, [fetch])

  return {
    ...asyncOp,
    fetch,
    refetch,
    isFetching: asyncOp.loading
  }
}

/**
 * Hook for managing multiple async operations
 */
export function useMultipleAsyncOperations() {
  const [operations, setOperations] = useState<Record<string, AsyncOperationState<any>>>({})

  const execute = useCallback(async <T>(
    key: string,
    operation: () => Promise<T>,
    options: UseAsyncOperationOptions = {}
  ): Promise<T | null> => {
    // Set loading state
    setOperations(prev => ({
      ...prev,
      [key]: {
        data: null,
        loading: true,
        error: null,
        success: false
      }
    }))

    try {
      const result = await operation()
      
      setOperations(prev => ({
        ...prev,
        [key]: {
          data: result,
          loading: false,
          error: null,
          success: true
        }
      }))

      if (options.onSuccess) {
        options.onSuccess(result)
      }

      return result
    } catch (error) {
      const errorInfo = handleAPIError(
        error, 
        options.context,
        options.showToast !== false
      )

      setOperations(prev => ({
        ...prev,
        [key]: {
          data: null,
          loading: false,
          error: errorInfo.message,
          success: false
        }
      }))

      if (options.onError) {
        options.onError(error)
      }

      return null
    }
  }, [])

  const getOperation = useCallback((key: string) => {
    return operations[key] || {
      data: null,
      loading: false,
      error: null,
      success: false
    }
  }, [operations])

  const reset = useCallback((key?: string) => {
    if (key) {
      setOperations(prev => {
        const newOps = { ...prev }
        delete newOps[key]
        return newOps
      })
    } else {
      setOperations({})
    }
  }, [])

  const isAnyLoading = Object.values(operations).some(op => op.loading)
  const hasAnyError = Object.values(operations).some(op => op.error)

  return {
    execute,
    getOperation,
    reset,
    isAnyLoading,
    hasAnyError,
    operations
  }
}

/**
 * Hook for managing paginated data with loading states
 */
export function usePaginatedData<T = any>(
  options: UseAsyncOperationOptions = {}
) {
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [allData, setAllData] = useState<T[]>([])
  
  const asyncOp = useAsyncOperation<{ data: T[], hasMore: boolean, total?: number }>(options)

  const loadPage = useCallback(async (
    pageNumber: number,
    fetchOperation: (page: number) => Promise<{ data: T[], hasMore: boolean, total?: number }>
  ): Promise<void> => {
    const result = await asyncOp.execute(
      () => fetchOperation(pageNumber),
      { ...options.context, action: 'paginated_fetch', additionalInfo: { page: pageNumber } }
    )

    if (result) {
      if (pageNumber === 1) {
        setAllData(result.data)
      } else {
        setAllData(prev => [...prev, ...result.data])
      }
      setHasMore(result.hasMore)
      setPage(pageNumber)
    }
  }, [asyncOp, options.context])

  const loadMore = useCallback(async (
    fetchOperation: (page: number) => Promise<{ data: T[], hasMore: boolean, total?: number }>
  ): Promise<void> => {
    if (hasMore && !asyncOp.loading) {
      await loadPage(page + 1, fetchOperation)
    }
  }, [hasMore, asyncOp.loading, page, loadPage])

  const refresh = useCallback(async (
    fetchOperation: (page: number) => Promise<{ data: T[], hasMore: boolean, total?: number }>
  ): Promise<void> => {
    setPage(1)
    setHasMore(true)
    setAllData([])
    await loadPage(1, fetchOperation)
  }, [loadPage])

  return {
    data: allData,
    loading: asyncOp.loading,
    error: asyncOp.error,
    success: asyncOp.success,
    page,
    hasMore,
    loadPage,
    loadMore,
    refresh,
    reset: () => {
      setPage(1)
      setHasMore(true)
      setAllData([])
      asyncOp.reset()
    }
  }
}
