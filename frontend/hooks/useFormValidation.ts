'use client'

import { useState, useCallback, useEffect } from 'react'
import { useForm, UseFormProps, FieldValues, Path, UseFormReturn } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { ZodSchema } from 'zod'
import { toast } from '@/components/ui/use-toast'

/**
 * Enhanced form validation hook with real-time validation and user feedback
 */

export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: any) => boolean | Promise<boolean>
  message?: string
}

export interface ValidationRules {
  [key: string]: ValidationRule
}

export interface UseFormValidationOptions<T extends FieldValues> extends UseFormProps<T> {
  schema?: ZodSchema<T>
  validationRules?: ValidationRules
  realTimeValidation?: boolean
  showSuccessMessages?: boolean
  onValidationChange?: (isValid: boolean, errors: Record<string, string>) => void
}

export interface FormValidationState {
  isValid: boolean
  errors: Record<string, string>
  touched: Record<string, boolean>
  isSubmitting: boolean
  submitCount: number
}

export function useFormValidation<T extends FieldValues>(
  options: UseFormValidationOptions<T> = {}
) {
  const {
    schema,
    validationRules,
    realTimeValidation = true,
    showSuccessMessages = false,
    onValidationChange,
    ...formOptions
  } = options

  // Initialize react-hook-form
  const form = useForm<T>({
    ...formOptions,
    resolver: schema ? zodResolver(schema) : undefined,
    mode: realTimeValidation ? 'onChange' : 'onSubmit'
  })

  const [validationState, setValidationState] = useState<FormValidationState>({
    isValid: false,
    errors: {},
    touched: {},
    isSubmitting: false,
    submitCount: 0
  })

  const [fieldValidationStatus, setFieldValidationStatus] = useState<Record<string, 'idle' | 'validating' | 'valid' | 'invalid'>>({})

  // Watch all form values for real-time validation
  const watchedValues = form.watch()

  // Update validation state when form errors change
  useEffect(() => {
    const errors = form.formState.errors
    const errorMessages: Record<string, string> = {}

    Object.keys(errors).forEach(key => {
      const error = errors[key as keyof typeof errors]
      if (error?.message) {
        errorMessages[key] = error.message as string
      }
    })

    const isValid = Object.keys(errorMessages).length === 0
    
    setValidationState(prev => ({
      ...prev,
      isValid,
      errors: errorMessages
    }))

    // Call validation change callback
    if (onValidationChange) {
      onValidationChange(isValid, errorMessages)
    }
  }, [form.formState.errors, onValidationChange])

  // Real-time field validation
  const validateField = useCallback(async (fieldName: string, value: any) => {
    if (!realTimeValidation || !validationRules?.[fieldName]) {
      return
    }

    setFieldValidationStatus(prev => ({
      ...prev,
      [fieldName]: 'validating'
    }))

    try {
      const rule = validationRules[fieldName]
      let isValid = true
      let errorMessage = ''

      // Required validation
      if (rule.required && (!value || (typeof value === 'string' && !value.trim()))) {
        isValid = false
        errorMessage = rule.message || 'هذا الحقل مطلوب'
      }

      // Length validation
      if (isValid && rule.minLength && value && value.length < rule.minLength) {
        isValid = false
        errorMessage = rule.message || `يجب أن يكون ${rule.minLength} أحرف على الأقل`
      }

      if (isValid && rule.maxLength && value && value.length > rule.maxLength) {
        isValid = false
        errorMessage = rule.message || `يجب أن يكون ${rule.maxLength} أحرف كحد أقصى`
      }

      // Pattern validation
      if (isValid && rule.pattern && value && !rule.pattern.test(value)) {
        isValid = false
        errorMessage = rule.message || 'تنسيق غير صحيح'
      }

      // Custom validation
      if (isValid && rule.custom) {
        const customResult = await rule.custom(value)
        if (!customResult) {
          isValid = false
          errorMessage = rule.message || 'قيمة غير صحيحة'
        }
      }

      setFieldValidationStatus(prev => ({
        ...prev,
        [fieldName]: isValid ? 'valid' : 'invalid'
      }))

      // Update form errors if validation failed
      if (!isValid) {
        form.setError(fieldName as Path<T>, {
          type: 'manual',
          message: errorMessage
        })
      } else {
        form.clearErrors(fieldName as Path<T>)
      }

    } catch (error) {
      console.error('Field validation error:', error)
      setFieldValidationStatus(prev => ({
        ...prev,
        [fieldName]: 'invalid'
      }))
    }
  }, [realTimeValidation, validationRules, form])

  // Handle field blur (mark as touched)
  const handleFieldBlur = useCallback((fieldName: string) => {
    setValidationState(prev => ({
      ...prev,
      touched: {
        ...prev.touched,
        [fieldName]: true
      }
    }))
  }, [])

  // Enhanced submit handler
  const handleSubmit = useCallback((
    onSubmit: (data: T) => Promise<void> | void,
    onError?: (errors: Record<string, string>) => void
  ) => {
    return form.handleSubmit(
      async (data) => {
        setValidationState(prev => ({
          ...prev,
          isSubmitting: true,
          submitCount: prev.submitCount + 1
        }))

        try {
          await onSubmit(data)
          
          if (showSuccessMessages) {
            toast({
              title: 'تم بنجاح',
              description: 'تم إرسال النموذج بنجاح',
              variant: 'default'
            })
          }
        } catch (error: any) {
          console.error('Form submission error:', error)
          
          toast({
            title: 'خطأ في الإرسال',
            description: error.message || 'حدث خطأ أثناء إرسال النموذج',
            variant: 'destructive'
          })
        } finally {
          setValidationState(prev => ({
            ...prev,
            isSubmitting: false
          }))
        }
      },
      (errors) => {
        const errorMessages: Record<string, string> = {}
        Object.keys(errors).forEach(key => {
          const error = errors[key as keyof typeof errors]
          if (error?.message) {
            errorMessages[key] = error.message as string
          }
        })

        // Show first error in toast
        const firstError = Object.values(errorMessages)[0]
        if (firstError) {
          toast({
            title: 'خطأ في البيانات',
            description: firstError,
            variant: 'destructive'
          })
        }

        if (onError) {
          onError(errorMessages)
        }
      }
    )
  }, [form, showSuccessMessages])

  // Get field validation status
  const getFieldStatus = useCallback((fieldName: string) => {
    const hasError = !!validationState.errors[fieldName]
    const isTouched = validationState.touched[fieldName]
    const status = fieldValidationStatus[fieldName] || 'idle'

    return {
      hasError,
      isTouched,
      isValid: status === 'valid' && !hasError,
      isValidating: status === 'validating',
      errorMessage: validationState.errors[fieldName],
      showError: hasError && (isTouched || validationState.submitCount > 0)
    }
  }, [validationState, fieldValidationStatus])

  // Enhanced register function with real-time validation
  const registerField = useCallback((fieldName: string, options?: any) => {
    const registration = form.register(fieldName as Path<T>, options)

    return {
      ...registration,
      onBlur: (e: any) => {
        registration.onBlur?.(e)
        handleFieldBlur(fieldName)
        if (realTimeValidation) {
          validateField(fieldName, e.target.value)
        }
      },
      onChange: (e: any) => {
        registration.onChange?.(e)
        if (realTimeValidation) {
          // Debounce validation for better performance
          setTimeout(() => {
            validateField(fieldName, e.target.value)
          }, 300)
        }
      }
    }
  }, [form, handleFieldBlur, validateField, realTimeValidation])

  // Reset validation state
  const resetValidation = useCallback(() => {
    setValidationState({
      isValid: false,
      errors: {},
      touched: {},
      isSubmitting: false,
      submitCount: 0
    })
    setFieldValidationStatus({})
    form.reset()
  }, [form])

  return {
    // Form methods
    ...form,
    
    // Enhanced methods
    register: registerField,
    handleSubmit,
    resetValidation,
    
    // Validation state
    validationState,
    getFieldStatus,
    
    // Utilities
    isValid: validationState.isValid,
    isSubmitting: validationState.isSubmitting,
    hasErrors: Object.keys(validationState.errors).length > 0,
    errorCount: Object.keys(validationState.errors).length,
    touchedCount: Object.keys(validationState.touched).length
  }
}
